# Apex Company Management System

A comprehensive company management system built with HTML, CSS, JavaScript, Bootstrap 5, PHP, and MySQLi. This system provides complete business management capabilities including client management, project tracking, invoicing, staff management, and more.

## Features

### 🔐 Authentication & Access Control
- Secure login/logout with session management
- Role-based access control (Super Admin, Admin, Staff)
- Permission management system
- Password reset functionality
- Account lockout protection

### 📊 Dashboard
- Overview cards with key metrics
- Recent activity tracking
- Quick access to important features
- Real-time notifications

### 👥 Staff Management
- Add/Edit/Delete staff members
- Role and permission assignment
- Activity tracking and login logs
- Status management (Active/Inactive)

### 🏢 Client Management
- Complete client profile management
- Contact information and history
- Project and invoice tracking
- Notes and attachments
- **GST number is optional** as requested

### 📋 Project Management
- Create and manage projects
- Assign to clients and staff
- Status tracking (Pending, Active, Completed, On Hold)
- Milestones and deadlines
- File uploads per project

### 🧾 Invoice Management
- Create professional invoices
- Auto-link to projects
- **Optional GST/Tax calculations**
- Email sending capability
- PDF generation
- Payment tracking

### 💬 Internal Communication
- One-on-one chat system
- Group messaging
- File sharing capabilities
- Real-time notifications

### 📈 Reports Module
- Sales reports (monthly, quarterly, yearly)
- Client analytics
- Staff activity reports
- Invoice status reports

### 📁 File Manager
- Organized file storage
- File preview and download
- Permission-based access
- Multiple file type support

### ⚙️ Settings
- Company profile management
- Payment settings
- Email templates
- System configuration

## System Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- MySQLi extension
- Write permissions for uploads and cache

## Installation

### Method 1: Using Installation Wizard (Recommended)

1. **Download and Extract**
   ```bash
   # Extract the files to your web server directory
   # For XAMPP: c:\xampp\htdocs\Myprojects\apex\
   ```

2. **Run Installation Wizard**
   - Open your browser and navigate to: `http://localhost/Myprojects/apex/install.php`
   - Follow the step-by-step installation wizard
   - Configure database settings
   - Create admin account
   - Complete installation

3. **Security**
   - Delete `install.php` file after installation
   - Set proper file permissions

### Method 2: Manual Installation

1. **Database Setup**
   ```sql
   -- Create database
   CREATE DATABASE apex_company_management;
   
   -- Import schema
   mysql -u username -p apex_company_management < database/schema.sql
   
   -- Import initial data
   mysql -u username -p apex_company_management < database/initial_data.sql
   ```

2. **Configuration**
   - Edit `config/config.php` with your database credentials
   - Update `APP_URL` to match your installation path

3. **Permissions**
   ```bash
   chmod 755 uploads/
   chmod 755 cache/
   ```

## Default Login Credentials

After installation, use these credentials to login:

- **Username:** admin
- **Password:** admin123
- **Email:** <EMAIL>

**⚠️ Important:** Change the default password immediately after first login!

## Configuration

### Database Configuration
Edit `config/config.php`:
```php
define('DB_HOST', 'localhost');
define('DB_USERNAME', 'your_username');
define('DB_PASSWORD', 'your_password');
define('DB_NAME', 'apex_company_management');
```

### Application Settings
```php
define('APP_URL', 'http://localhost/Myprojects/apex/');
define('UPLOAD_PATH', __DIR__ . '/../uploads/');
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10MB
```

### Security Settings
```php
define('SESSION_TIMEOUT', 3600); // 1 hour
define('PASSWORD_MIN_LENGTH', 8);
define('LOGIN_ATTEMPTS_LIMIT', 5);
```

## Usage

### User Roles

1. **Super Admin**
   - Full system access
   - User management
   - System settings
   - All permissions

2. **Admin**
   - Most features except user management
   - Cannot delete users or modify system settings
   - Can manage clients, projects, invoices

3. **Staff**
   - Basic access to assigned features
   - Can view and create clients, projects
   - Limited reporting access

### Key Features Usage

#### Client Management
- Navigate to Clients → Add New
- Fill in company details
- **GST number is optional** - leave blank if not applicable
- Assign to staff members
- Track project history

#### Project Management
- Create projects linked to clients
- Assign project managers and team members
- Set milestones and deadlines
- Upload project files
- Track progress

#### Invoice Generation
- Create invoices from projects or standalone
- Add line items with descriptions
- **GST/Tax is optional** - set rate to 0 if not applicable
- Send via email or download PDF
- Track payment status

#### Communication
- Use internal chat for team communication
- Share files within conversations
- Create group chats for project teams
- Receive real-time notifications

## File Structure

```
apex/
├── api/                    # API endpoints
├── assets/                 # CSS, JS, images
├── auth/                   # Authentication pages
├── cache/                  # Cache files
├── clients/                # Client management
├── config/                 # Configuration files
├── dashboard/              # Dashboard
├── database/               # Database schema and data
├── files/                  # File manager
├── includes/               # Common includes
├── invoices/               # Invoice management
├── messages/               # Communication system
├── projects/               # Project management
├── reports/                # Reports and analytics
├── settings/               # System settings
├── staff/                  # Staff management
├── uploads/                # File uploads
└── index.php              # Entry point
```

## Security Features

- CSRF protection on all forms
- SQL injection prevention
- XSS protection
- Session management
- Rate limiting
- File upload validation
- Password strength requirements
- Account lockout protection

## Customization

### Adding New Permissions
1. Add permission to `database/initial_data.sql`
2. Assign to appropriate roles
3. Use `has_permission()` function in code

### Custom Themes
- Modify `assets/css/style.css`
- Update color variables in `:root`
- Customize Bootstrap components

### Email Templates
- Configure SMTP settings in `config/config.php`
- Customize templates in settings

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check database credentials in `config/config.php`
   - Ensure MySQL service is running
   - Verify database exists

2. **Permission Denied**
   - Check file permissions on uploads/ and cache/
   - Ensure web server has write access

3. **Session Issues**
   - Check PHP session configuration
   - Verify session timeout settings

4. **File Upload Problems**
   - Check `MAX_FILE_SIZE` setting
   - Verify allowed file types
   - Check upload directory permissions

### Debug Mode
Enable debug mode in `config/config.php`:
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

## Support

For support and questions:
- Check the documentation
- Review error logs
- Verify system requirements
- Ensure proper installation steps

## License

This project is proprietary software. All rights reserved.

## Changelog

### Version 1.0.0
- Initial release
- Complete company management system
- All core features implemented
- Security features included
- Responsive design
- Optional GST handling as requested
