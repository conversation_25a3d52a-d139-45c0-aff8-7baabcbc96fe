/* Apex Company Management System - Custom Styles */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --sidebar-width: 250px;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    padding-top: 76px; /* Account for fixed navbar */
}

.main-wrapper {
    min-height: calc(100vh - 140px);
    padding: 20px 0;
}

/* Navigation Styles */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    margin: 0 0.25rem;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.dropdown-menu {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-radius: 0.5rem;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background-color: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
    padding: 1.25rem;
}

/* Dashboard Cards */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.stats-card.success {
    background: linear-gradient(135deg, var(--success-color), #146c43);
}

.stats-card.warning {
    background: linear-gradient(135deg, var(--warning-color), #e6a700);
}

.stats-card.danger {
    background: linear-gradient(135deg, var(--danger-color), #b02a37);
}

.stats-card .stats-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.stats-card .stats-number {
    font-size: 2rem;
    font-weight: 700;
    margin: 0.5rem 0;
}

.stats-card .stats-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

/* Form Styles */
.form-control, .form-select {
    border-radius: 0.5rem;
    border: 1px solid #dee2e6;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.input-group-text {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
}

/* Button Styles */
.btn {
    border-radius: 0.5rem;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #146c43);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #b02a37);
    border: none;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* Table Styles */
.table {
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    padding: 1rem;
}

.table tbody td {
    padding: 1rem;
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* Badge Styles */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
}

/* Status Badges */
.status-active { background-color: var(--success-color); }
.status-inactive { background-color: var(--secondary-color); }
.status-pending { background-color: var(--warning-color); color: #000; }
.status-completed { background-color: var(--success-color); }
.status-cancelled { background-color: var(--danger-color); }
.status-on-hold { background-color: var(--info-color); color: #000; }
.status-draft { background-color: var(--secondary-color); }
.status-sent { background-color: var(--info-color); color: #000; }
.status-paid { background-color: var(--success-color); }
.status-overdue { background-color: var(--danger-color); }

/* Progress Bar */
.progress {
    height: 0.75rem;
    border-radius: 0.5rem;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 0.5rem;
}

/* File Upload */
.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.75rem;
    padding: 2rem;
    text-align: center;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.05);
}

.file-upload-area.dragover {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.1);
}

/* Chat Styles */
.chat-container {
    height: 400px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.75rem;
    padding: 1rem;
    background-color: white;
}

.message {
    margin-bottom: 1rem;
    padding: 0.75rem;
    border-radius: 0.75rem;
    max-width: 70%;
}

.message.sent {
    background-color: var(--primary-color);
    color: white;
    margin-left: auto;
    text-align: right;
}

.message.received {
    background-color: #f8f9fa;
    color: #495057;
}

.message-time {
    font-size: 0.75rem;
    opacity: 0.7;
    margin-top: 0.25rem;
}

/* Notification Styles */
.notification-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #dee2e6;
    transition: background-color 0.3s ease;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item.unread {
    background-color: rgba(13, 110, 253, 0.05);
    border-left: 3px solid var(--primary-color);
}

/* Loading Spinner */
.spinner-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

/* Responsive Design */
@media (max-width: 768px) {
    body {
        padding-top: 60px;
    }
    
    .main-wrapper {
        padding: 10px 0;
    }
    
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        border-radius: 0.75rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
}

/* Print Styles */
@media print {
    .navbar, .footer, .btn, .dropdown {
        display: none !important;
    }
    
    body {
        padding-top: 0;
    }
    
    .main-wrapper {
        padding: 0;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Utility Classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.cursor-pointer {
    cursor: pointer;
}

.border-dashed {
    border-style: dashed !important;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
}

.bg-gradient-success {
    background: linear-gradient(135deg, var(--success-color), #146c43);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, var(--warning-color), #e6a700);
}

.bg-gradient-danger {
    background: linear-gradient(135deg, var(--danger-color), #b02a37);
}
