/**
 * Apex Company Management System - Main JavaScript
 */

// Global variables
let csrfToken = '';

$(document).ready(function() {
    // Initialize components
    initializeComponents();
    
    // Set up event listeners
    setupEventListeners();
    
    // Load initial data
    loadInitialData();
});

/**
 * Initialize components
 */
function initializeComponents() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Initialize file upload areas
    initializeFileUpload();
    
    // Initialize data tables
    if (typeof $.fn.DataTable !== 'undefined') {
        $('.data-table').DataTable({
            responsive: true,
            pageLength: 25,
            order: [[0, 'desc']],
            language: {
                search: "Search:",
                lengthMenu: "Show _MENU_ entries",
                info: "Showing _START_ to _END_ of _TOTAL_ entries",
                paginate: {
                    first: "First",
                    last: "Last",
                    next: "Next",
                    previous: "Previous"
                }
            }
        });
    }
}

/**
 * Setup event listeners
 */
function setupEventListeners() {
    // Form submission with loading state
    $('form').on('submit', function() {
        let submitBtn = $(this).find('button[type="submit"]');
        if (submitBtn.length) {
            submitBtn.prop('disabled', true);
            submitBtn.html('<span class="spinner-border spinner-border-sm me-2"></span>Processing...');
        }
    });
    
    // Confirm delete actions
    $(document).on('click', '.confirm-delete', function(e) {
        e.preventDefault();
        
        let message = $(this).data('message') || 'Are you sure you want to delete this item?';
        let url = $(this).attr('href');
        
        if (confirm(message)) {
            window.location.href = url;
        }
    });
    
    // Auto-resize textareas
    $('textarea').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
    
    // Number formatting
    $('.currency-input').on('input', function() {
        let value = $(this).val().replace(/[^\d.]/g, '');
        $(this).val(value);
    });
    
    // Phone number formatting
    $('.phone-input').on('input', function() {
        let value = $(this).val().replace(/[^\d+\-\s()]/g, '');
        $(this).val(value);
    });
    
    // Search functionality
    $('.search-input').on('input', debounce(function() {
        let query = $(this).val();
        let target = $(this).data('target');
        
        if (query.length >= 2) {
            performSearch(query, target);
        } else {
            clearSearch(target);
        }
    }, 300));
}

/**
 * Load initial data
 */
function loadInitialData() {
    // Get CSRF token
    $.get('/api/csrf-token.php', function(data) {
        if (data.success) {
            csrfToken = data.token;
        }
    });
}

/**
 * Initialize file upload functionality
 */
function initializeFileUpload() {
    $('.file-upload-area').on('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('dragover');
    });
    
    $('.file-upload-area').on('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
    });
    
    $('.file-upload-area').on('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
        
        let files = e.originalEvent.dataTransfer.files;
        let input = $(this).find('input[type="file"]')[0];
        
        if (input && files.length > 0) {
            input.files = files;
            handleFileSelect(input);
        }
    });
    
    $('input[type="file"]').on('change', function() {
        handleFileSelect(this);
    });
}

/**
 * Handle file selection
 */
function handleFileSelect(input) {
    let files = input.files;
    let container = $(input).closest('.file-upload-area');
    let preview = container.find('.file-preview');
    
    if (!preview.length) {
        preview = $('<div class="file-preview mt-3"></div>');
        container.append(preview);
    }
    
    preview.empty();
    
    for (let i = 0; i < files.length; i++) {
        let file = files[i];
        let fileItem = $(`
            <div class="file-item d-flex align-items-center mb-2">
                <i class="bi bi-file-earmark me-2"></i>
                <span class="file-name">${file.name}</span>
                <span class="file-size ms-auto text-muted">${formatFileSize(file.size)}</span>
            </div>
        `);
        
        preview.append(fileItem);
    }
}

/**
 * Format file size
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Perform search
 */
function performSearch(query, target) {
    showLoading();
    
    $.get('/api/search.php', {
        q: query,
        target: target
    }, function(data) {
        hideLoading();
        
        if (data.success) {
            displaySearchResults(data.results, target);
        }
    });
}

/**
 * Clear search results
 */
function clearSearch(target) {
    $(target).empty();
}

/**
 * Display search results
 */
function displaySearchResults(results, target) {
    let container = $(target);
    container.empty();
    
    if (results.length === 0) {
        container.append('<div class="text-muted">No results found</div>');
        return;
    }
    
    results.forEach(function(result) {
        let item = $(`
            <div class="search-result-item p-2 border-bottom">
                <div class="fw-bold">${result.title}</div>
                <div class="text-muted small">${result.description}</div>
            </div>
        `);
        
        item.on('click', function() {
            if (result.url) {
                window.location.href = result.url;
            }
        });
        
        container.append(item);
    });
}

/**
 * Show loading spinner
 */
function showLoading() {
    if ($('.spinner-overlay').length === 0) {
        $('body').append(`
            <div class="spinner-overlay">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        `);
    }
}

/**
 * Hide loading spinner
 */
function hideLoading() {
    $('.spinner-overlay').remove();
}

/**
 * Show toast notification
 */
function showToast(message, type = 'success') {
    let toastHtml = `
        <div class="toast align-items-center text-white bg-${type} border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    
    let toastContainer = $('.toast-container');
    if (toastContainer.length === 0) {
        toastContainer = $('<div class="toast-container position-fixed top-0 end-0 p-3"></div>');
        $('body').append(toastContainer);
    }
    
    let toast = $(toastHtml);
    toastContainer.append(toast);
    
    let bsToast = new bootstrap.Toast(toast[0]);
    bsToast.show();
    
    toast.on('hidden.bs.toast', function() {
        $(this).remove();
    });
}

/**
 * Debounce function
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Format currency
 */
function formatCurrency(amount, currency = 'INR') {
    return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

/**
 * Format date
 */
function formatDate(date, format = 'dd-mm-yyyy') {
    if (!date) return '-';
    
    let d = new Date(date);
    let day = String(d.getDate()).padStart(2, '0');
    let month = String(d.getMonth() + 1).padStart(2, '0');
    let year = d.getFullYear();
    
    switch (format) {
        case 'dd-mm-yyyy':
            return `${day}-${month}-${year}`;
        case 'mm-dd-yyyy':
            return `${month}-${day}-${year}`;
        case 'yyyy-mm-dd':
            return `${year}-${month}-${day}`;
        default:
            return d.toLocaleDateString();
    }
}

/**
 * Validate form
 */
function validateForm(form) {
    let isValid = true;
    let errors = [];
    
    $(form).find('[required]').each(function() {
        if (!$(this).val().trim()) {
            isValid = false;
            errors.push(`${$(this).data('label') || 'Field'} is required`);
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });
    
    // Email validation
    $(form).find('input[type="email"]').each(function() {
        let email = $(this).val().trim();
        if (email && !isValidEmail(email)) {
            isValid = false;
            errors.push('Please enter a valid email address');
            $(this).addClass('is-invalid');
        }
    });
    
    // Phone validation
    $(form).find('.phone-input').each(function() {
        let phone = $(this).val().trim();
        if (phone && !isValidPhone(phone)) {
            isValid = false;
            errors.push('Please enter a valid phone number');
            $(this).addClass('is-invalid');
        }
    });
    
    if (!isValid) {
        showToast(errors.join('<br>'), 'danger');
    }
    
    return isValid;
}

/**
 * Validate email
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Validate phone
 */
function isValidPhone(phone) {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
}

/**
 * Copy to clipboard
 */
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showToast('Copied to clipboard!', 'success');
    }).catch(function() {
        showToast('Failed to copy to clipboard', 'danger');
    });
}

/**
 * Export table to CSV
 */
function exportTableToCSV(tableId, filename = 'export.csv') {
    let csv = [];
    let rows = document.querySelectorAll(`#${tableId} tr`);
    
    for (let i = 0; i < rows.length; i++) {
        let row = [], cols = rows[i].querySelectorAll('td, th');
        
        for (let j = 0; j < cols.length; j++) {
            row.push(cols[j].innerText);
        }
        
        csv.push(row.join(','));
    }
    
    downloadCSV(csv.join('\n'), filename);
}

/**
 * Download CSV
 */
function downloadCSV(csv, filename) {
    let csvFile = new Blob([csv], { type: 'text/csv' });
    let downloadLink = document.createElement('a');
    
    downloadLink.download = filename;
    downloadLink.href = window.URL.createObjectURL(csvFile);
    downloadLink.style.display = 'none';
    
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
}
