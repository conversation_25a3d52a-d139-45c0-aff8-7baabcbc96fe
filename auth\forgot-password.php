<?php
/**
 * Apex Company Management System
 * Forgot Password Page
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';

// Redirect if already logged in
if (is_logged_in()) {
    header('Location: ../dashboard/');
    exit();
}

$error = '';
$success = '';
$step = $_GET['step'] ?? 'request';

// Handle password reset request
if ($_POST && $step === 'request') {
    $email = sanitize_input($_POST['email'] ?? '');
    
    if (empty($email) || !validate_email($email)) {
        $error = 'Please enter a valid email address';
    } else {
        // Check rate limiting
        if (!check_rate_limit('password_reset', 3, 3600)) {
            $error = 'Too many password reset attempts. Please try again later.';
        } else {
            // Check if email exists
            $stmt = $mysqli->prepare("SELECT id, first_name, last_name FROM users WHERE email = ? AND status = 'active'");
            $stmt->bind_param("s", $email);
            $stmt->execute();
            $result = $stmt->get_result();
            $user = $result->fetch_assoc();
            
            if ($user) {
                // Generate reset token
                $reset_token = generate_token();
                $reset_expires = date('Y-m-d H:i:s', time() + 3600); // 1 hour
                
                // Store reset token
                $stmt = $mysqli->prepare("UPDATE users SET reset_token = ?, reset_expires = ? WHERE id = ?");
                $stmt->bind_param("ssi", $reset_token, $reset_expires, $user['id']);
                $stmt->execute();
                
                // Send reset email (you'll need to implement email sending)
                $reset_link = APP_URL . "auth/forgot-password.php?step=reset&token=" . $reset_token;
                
                // For now, just show the link (in production, send via email)
                $success = "Password reset instructions have been sent to your email address. 
                           <br><br><strong>For testing:</strong> <a href='$reset_link'>Click here to reset password</a>";
                
                // Log activity
                log_activity('Password Reset Requested', 'users', $user['id']);
            } else {
                // Don't reveal if email exists or not for security
                $success = "If an account with that email exists, password reset instructions have been sent.";
            }
        }
    }
}

// Handle password reset
if ($_POST && $step === 'reset') {
    $token = sanitize_input($_GET['token'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    
    if (empty($token)) {
        $error = 'Invalid reset token';
    } elseif (empty($password) || empty($confirm_password)) {
        $error = 'Please enter and confirm your new password';
    } elseif ($password !== $confirm_password) {
        $error = 'Passwords do not match';
    } else {
        // Validate password strength
        $password_errors = validate_password_strength($password);
        if (!empty($password_errors)) {
            $error = implode('<br>', $password_errors);
        } else {
            // Verify reset token
            $stmt = $mysqli->prepare("
                SELECT id, first_name, last_name 
                FROM users 
                WHERE reset_token = ? AND reset_expires > NOW() AND status = 'active'
            ");
            $stmt->bind_param("s", $token);
            $stmt->execute();
            $result = $stmt->get_result();
            $user = $result->fetch_assoc();
            
            if ($user) {
                // Update password
                $password_hash = hash_password($password);
                $stmt = $mysqli->prepare("
                    UPDATE users 
                    SET password_hash = ?, reset_token = NULL, reset_expires = NULL, login_attempts = 0, locked_until = NULL 
                    WHERE id = ?
                ");
                $stmt->bind_param("si", $password_hash, $user['id']);
                $stmt->execute();
                
                // Log activity
                log_activity('Password Reset Completed', 'users', $user['id']);
                
                $success = 'Your password has been successfully reset. You can now login with your new password.';
                $step = 'complete';
            } else {
                $error = 'Invalid or expired reset token';
            }
        }
    }
}

$page_title = 'Forgot Password';
$body_class = 'forgot-password-page';
$hide_navigation = true;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title . ' - ' . APP_NAME; ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="<?php echo APP_URL; ?>assets/css/style.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            padding-top: 0;
        }
        .forgot-password-container {
            max-width: 450px;
            margin: 0 auto;
        }
        .forgot-password-card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        .forgot-password-header {
            background: linear-gradient(135deg, #dc3545, #b02a37);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 2rem;
            text-align: center;
        }
        .forgot-password-body {
            padding: 2rem;
        }
        .icon-container {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 2rem;
        }
    </style>
</head>
<body>
    <div class="container forgot-password-container">
        <div class="card forgot-password-card">
            <div class="forgot-password-header">
                <div class="icon-container">
                    <i class="bi bi-key"></i>
                </div>
                <h3 class="mb-0">
                    <?php if ($step === 'request'): ?>
                        Forgot Password
                    <?php elseif ($step === 'reset'): ?>
                        Reset Password
                    <?php else: ?>
                        Password Reset
                    <?php endif; ?>
                </h3>
                <p class="mb-0 opacity-75">
                    <?php if ($step === 'request'): ?>
                        Enter your email to reset password
                    <?php elseif ($step === 'reset'): ?>
                        Enter your new password
                    <?php else: ?>
                        Password successfully reset
                    <?php endif; ?>
                </p>
            </div>
            
            <div class="forgot-password-body">
                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i>
                        <?php echo $error; ?>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle"></i>
                        <?php echo $success; ?>
                    </div>
                <?php endif; ?>

                <?php if ($step === 'request'): ?>
                    <form method="POST" class="needs-validation" novalidate>
                        <div class="mb-3">
                            <label for="email" class="form-label">
                                <i class="bi bi-envelope"></i> Email Address
                            </label>
                            <input type="email" 
                                   class="form-control" 
                                   id="email" 
                                   name="email" 
                                   value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                                   required 
                                   autofocus>
                            <div class="invalid-feedback">
                                Please enter a valid email address.
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-danger btn-lg w-100 mb-3">
                            <i class="bi bi-envelope"></i>
                            Send Reset Instructions
                        </button>
                    </form>

                <?php elseif ($step === 'reset'): ?>
                    <form method="POST" class="needs-validation" novalidate>
                        <div class="mb-3">
                            <label for="password" class="form-label">
                                <i class="bi bi-lock"></i> New Password
                            </label>
                            <div class="input-group">
                                <input type="password" 
                                       class="form-control" 
                                       id="password" 
                                       name="password" 
                                       required>
                                <button class="btn btn-outline-secondary" 
                                        type="button" 
                                        id="togglePassword">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                            <div class="form-text">
                                Password must be at least 8 characters with uppercase, lowercase, number, and special character.
                            </div>
                            <div class="invalid-feedback">
                                Please enter a strong password.
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">
                                <i class="bi bi-lock-fill"></i> Confirm Password
                            </label>
                            <input type="password" 
                                   class="form-control" 
                                   id="confirm_password" 
                                   name="confirm_password" 
                                   required>
                            <div class="invalid-feedback">
                                Please confirm your password.
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-success btn-lg w-100 mb-3">
                            <i class="bi bi-check-circle"></i>
                            Reset Password
                        </button>
                    </form>
                <?php endif; ?>

                <?php if ($step === 'complete' || $success): ?>
                    <div class="text-center">
                        <a href="login.php" class="btn btn-primary btn-lg">
                            <i class="bi bi-box-arrow-in-right"></i>
                            Back to Login
                        </a>
                    </div>
                <?php else: ?>
                    <div class="text-center">
                        <a href="login.php" class="text-decoration-none">
                            <i class="bi bi-arrow-left"></i>
                            Back to Login
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

    <script>
        $(document).ready(function() {
            // Toggle password visibility
            $('#togglePassword').on('click', function() {
                const password = $('#password');
                const icon = $(this).find('i');
                
                if (password.attr('type') === 'password') {
                    password.attr('type', 'text');
                    icon.removeClass('bi-eye').addClass('bi-eye-slash');
                } else {
                    password.attr('type', 'password');
                    icon.removeClass('bi-eye-slash').addClass('bi-eye');
                }
            });
            
            // Password confirmation validation
            $('#confirm_password').on('input', function() {
                const password = $('#password').val();
                const confirmPassword = $(this).val();
                
                if (password !== confirmPassword) {
                    $(this).addClass('is-invalid');
                    $(this).siblings('.invalid-feedback').text('Passwords do not match');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });
            
            // Form validation
            $('.needs-validation').on('submit', function(e) {
                if (!this.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                $(this).addClass('was-validated');
            });
            
            // Auto-hide alerts
            setTimeout(function() {
                $('.alert').fadeOut();
            }, 10000);
        });
    </script>
</body>
</html>
