<?php
/**
 * Apex Company Management System
 * Logout Page
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';

// Check if user is logged in
if (is_logged_in()) {
    // Log activity
    log_activity('User Logout', 'users', $_SESSION['user_id']);
    
    // Destroy user session
    destroy_user_session($_SESSION['user_id']);
    
    // Clear remember me token
    clear_remember_token($_SESSION['user_id']);
    
    // Clear all session data
    session_unset();
    session_destroy();
    
    // Start new session for flash message
    session_start();
    $_SESSION['success'] = 'You have been successfully logged out.';
}

// Redirect to login page
header('Location: login.php');
exit();
?>
