<?php
/**
 * Apex Company Management System
 * Delete Client
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';
require_once '../includes/middleware.php';

// Check authentication and permission
auth_middleware();
permission_middleware('delete_clients');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Verify CSRF token
if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Invalid security token']);
    exit();
}

$client_id = (int)($_POST['id'] ?? 0);

if (!$client_id) {
    echo json_encode(['success' => false, 'message' => 'Invalid client ID']);
    exit();
}

try {
    // Check if client exists
    $stmt = $mysqli->prepare("SELECT company_name FROM clients WHERE id = ?");
    $stmt->bind_param("i", $client_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $client = $result->fetch_assoc();
    
    if (!$client) {
        echo json_encode(['success' => false, 'message' => 'Client not found']);
        exit();
    }
    
    // Check if client has associated projects
    $stmt = $mysqli->prepare("SELECT COUNT(*) as count FROM projects WHERE client_id = ?");
    $stmt->bind_param("i", $client_id);
    $stmt->execute();
    $project_count = $stmt->get_result()->fetch_assoc()['count'];
    
    // Check if client has associated invoices
    $stmt = $mysqli->prepare("SELECT COUNT(*) as count FROM invoices WHERE client_id = ?");
    $stmt->bind_param("i", $client_id);
    $stmt->execute();
    $invoice_count = $stmt->get_result()->fetch_assoc()['count'];
    
    if ($project_count > 0 || $invoice_count > 0) {
        echo json_encode([
            'success' => false, 
            'message' => 'Cannot delete client. Client has ' . $project_count . ' project(s) and ' . $invoice_count . ' invoice(s) associated with it. Please delete or reassign them first.'
        ]);
        exit();
    }
    
    // Delete the client
    $stmt = $mysqli->prepare("DELETE FROM clients WHERE id = ?");
    $stmt->bind_param("i", $client_id);
    
    if ($stmt->execute()) {
        // Log the activity
        log_activity('Client Deleted', 'clients', $client_id, ['company_name' => $client['company_name']]);
        
        echo json_encode([
            'success' => true, 
            'message' => 'Client deleted successfully'
        ]);
    } else {
        echo json_encode([
            'success' => false, 
            'message' => 'Error deleting client: ' . $mysqli->error
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false, 
        'message' => 'Error deleting client: ' . $e->getMessage()
    ]);
}
?>
