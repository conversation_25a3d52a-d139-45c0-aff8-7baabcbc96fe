<?php
/**
 * Apex Company Management System
 * Clients List
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';
require_once '../includes/middleware.php';

// Check authentication and permission
auth_middleware();
permission_middleware('view_clients');

$page_title = 'Clients';
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-people"></i>
                    Clients
                </h1>
                <?php if (has_permission('create_clients')): ?>
                <a href="add.php" class="btn btn-primary">
                    <i class="bi bi-plus"></i>
                    Add New Client
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">All Clients</h5>
                        <div class="d-flex gap-2">
                            <input type="text" class="form-control form-control-sm" placeholder="Search clients..." id="searchClients" style="width: 200px;">
                            <select class="form-select form-select-sm" id="statusFilter" style="width: 150px;">
                                <option value="">All Status</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <?php
                    // Get clients from database
                    $search = $_GET['search'] ?? '';
                    $status_filter = $_GET['status'] ?? '';

                    $query = "SELECT * FROM clients WHERE 1=1";
                    $params = [];
                    $types = "";

                    if (!empty($search)) {
                        $query .= " AND (company_name LIKE ? OR contact_person LIKE ? OR email LIKE ?)";
                        $search_param = "%$search%";
                        $params = array_merge($params, [$search_param, $search_param, $search_param]);
                        $types .= "sss";
                    }

                    if (!empty($status_filter)) {
                        $query .= " AND status = ?";
                        $params[] = $status_filter;
                        $types .= "s";
                    }

                    $query .= " ORDER BY created_at DESC";

                    $stmt = $mysqli->prepare($query);
                    if (!empty($params)) {
                        $stmt->bind_param($types, ...$params);
                    }
                    $stmt->execute();
                    $result = $stmt->get_result();
                    ?>

                    <?php if ($result->num_rows > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Company</th>
                                    <th>Contact Person</th>
                                    <th>Email</th>
                                    <th>Phone</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($client = $result->fetch_assoc()): ?>
                                <tr>
                                    <td>
                                        <strong><?= htmlspecialchars($client['company_name']) ?></strong>
                                    </td>
                                    <td><?= htmlspecialchars($client['contact_person']) ?></td>
                                    <td>
                                        <a href="mailto:<?= htmlspecialchars($client['email']) ?>">
                                            <?= htmlspecialchars($client['email']) ?>
                                        </a>
                                    </td>
                                    <td><?= htmlspecialchars($client['phone']) ?></td>
                                    <td>
                                        <span class="badge bg-<?= $client['status'] == 'active' ? 'success' : 'secondary' ?>">
                                            <?= ucfirst($client['status']) ?>
                                        </span>
                                    </td>
                                    <td><?= date('M j, Y', strtotime($client['created_at'])) ?></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="view.php?id=<?= $client['id'] ?>" class="btn btn-outline-primary" title="View">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <?php if (has_permission('edit_clients')): ?>
                                            <a href="edit.php?id=<?= $client['id'] ?>" class="btn btn-outline-warning" title="Edit">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <?php endif; ?>
                                            <?php if (has_permission('delete_clients')): ?>
                                            <button class="btn btn-outline-danger" onclick="deleteClient(<?= $client['id'] ?>)" title="Delete">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="text-center text-muted py-5">
                        <i class="bi bi-people" style="font-size: 4rem;"></i>
                        <h4 class="mt-3">No Clients Found</h4>
                        <p>You haven't added any clients yet.</p>
                        <?php if (has_permission('create_clients')): ?>
                        <a href="add.php" class="btn btn-primary mt-3">
                            <i class="bi bi-plus"></i>
                            Add First Client
                        </a>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Search functionality
document.getElementById('searchClients').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const rows = document.querySelectorAll('tbody tr');

    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
    });
});

// Status filter
document.getElementById('statusFilter').addEventListener('change', function() {
    const filterValue = this.value.toLowerCase();
    const rows = document.querySelectorAll('tbody tr');

    rows.forEach(row => {
        if (filterValue === '') {
            row.style.display = '';
        } else {
            const statusBadge = row.querySelector('.badge');
            const status = statusBadge ? statusBadge.textContent.toLowerCase() : '';
            row.style.display = status.includes(filterValue) ? '' : 'none';
        }
    });
});

// Delete client function
function deleteClient(clientId) {
    if (confirm('Are you sure you want to delete this client? This action cannot be undone.')) {
        fetch('delete.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'id=' + clientId + '&csrf_token=' + document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error deleting client: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error deleting client: ' + error.message);
        });
    }
}
</script>

<?php require_once '../includes/footer.php'; ?>
