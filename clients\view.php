<?php
/**
 * Apex Company Management System
 * View Client
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';
require_once '../includes/middleware.php';

// Check authentication and permission
auth_middleware();
permission_middleware('view_clients');

$client_id = (int)($_GET['id'] ?? 0);

if (!$client_id) {
    $_SESSION['error'] = 'Invalid client ID.';
    header('Location: index.php');
    exit();
}

// Get client details
$stmt = $mysqli->prepare("
    SELECT c.*, u.first_name, u.last_name 
    FROM clients c 
    LEFT JOIN users u ON c.created_by = u.id 
    WHERE c.id = ?
");
$stmt->bind_param("i", $client_id);
$stmt->execute();
$result = $stmt->get_result();
$client = $result->fetch_assoc();

if (!$client) {
    $_SESSION['error'] = 'Client not found.';
    header('Location: index.php');
    exit();
}

// Get client projects
$stmt = $mysqli->prepare("
    SELECT p.*, u.first_name as assigned_first_name, u.last_name as assigned_last_name
    FROM projects p 
    LEFT JOIN users u ON p.assigned_to = u.id 
    WHERE p.client_id = ? 
    ORDER BY p.created_at DESC
");
$stmt->bind_param("i", $client_id);
$stmt->execute();
$projects = $stmt->get_result();

// Get client invoices
$stmt = $mysqli->prepare("
    SELECT * FROM invoices 
    WHERE client_id = ? 
    ORDER BY created_at DESC
");
$stmt->bind_param("i", $client_id);
$stmt->execute();
$invoices = $stmt->get_result();

$page_title = 'View Client - ' . $client['company_name'];
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-person"></i>
                    <?= htmlspecialchars($client['company_name']) ?>
                </h1>
                <div class="d-flex gap-2">
                    <?php if (has_permission('edit_clients')): ?>
                    <a href="edit.php?id=<?= $client['id'] ?>" class="btn btn-warning">
                        <i class="bi bi-pencil"></i>
                        Edit Client
                    </a>
                    <?php endif; ?>
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Back to Clients
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Client Information -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-info-circle"></i>
                        Client Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">Company Name:</td>
                                    <td><?= htmlspecialchars($client['company_name']) ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Contact Person:</td>
                                    <td><?= htmlspecialchars($client['contact_person']) ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Email:</td>
                                    <td>
                                        <a href="mailto:<?= htmlspecialchars($client['email']) ?>">
                                            <?= htmlspecialchars($client['email']) ?>
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Phone:</td>
                                    <td>
                                        <?php if ($client['phone']): ?>
                                            <a href="tel:<?= htmlspecialchars($client['phone']) ?>">
                                                <?= htmlspecialchars($client['phone']) ?>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">Not provided</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Website:</td>
                                    <td>
                                        <?php if ($client['website']): ?>
                                            <a href="<?= htmlspecialchars($client['website']) ?>" target="_blank">
                                                <?= htmlspecialchars($client['website']) ?>
                                                <i class="bi bi-box-arrow-up-right"></i>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">Not provided</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">Address:</td>
                                    <td>
                                        <?php if ($client['address']): ?>
                                            <?= nl2br(htmlspecialchars($client['address'])) ?>
                                            <?php if ($client['city'] || $client['state'] || $client['pincode']): ?>
                                                <br>
                                                <?= htmlspecialchars($client['city']) ?>
                                                <?= $client['city'] && $client['state'] ? ', ' : '' ?>
                                                <?= htmlspecialchars($client['state']) ?>
                                                <?= $client['pincode'] ? ' - ' . htmlspecialchars($client['pincode']) : '' ?>
                                            <?php endif; ?>
                                            <?php if ($client['country']): ?>
                                                <br><?= htmlspecialchars($client['country']) ?>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="text-muted">Not provided</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">GST Number:</td>
                                    <td>
                                        <?= $client['gst_number'] ? htmlspecialchars($client['gst_number']) : '<span class="text-muted">Not provided</span>' ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Status:</td>
                                    <td>
                                        <span class="badge bg-<?= $client['status'] == 'active' ? 'success' : 'secondary' ?>">
                                            <?= ucfirst($client['status']) ?>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Created:</td>
                                    <td><?= format_datetime($client['created_at']) ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Created By:</td>
                                    <td>
                                        <?= $client['first_name'] ? htmlspecialchars($client['first_name'] . ' ' . $client['last_name']) : 'Unknown' ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <?php if ($client['notes']): ?>
                    <div class="mt-3">
                        <h6>Notes:</h6>
                        <div class="bg-light p-3 rounded">
                            <?= nl2br(htmlspecialchars($client['notes'])) ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Projects -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-kanban"></i>
                        Projects
                    </h5>
                    <?php if (has_permission('create_projects')): ?>
                    <a href="../projects/add.php?client_id=<?= $client['id'] ?>" class="btn btn-sm btn-primary">
                        <i class="bi bi-plus"></i>
                        New Project
                    </a>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <?php if ($projects->num_rows > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Project Name</th>
                                    <th>Status</th>
                                    <th>Assigned To</th>
                                    <th>Progress</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($project = $projects->fetch_assoc()): ?>
                                <tr>
                                    <td>
                                        <strong><?= htmlspecialchars($project['project_name']) ?></strong>
                                        <?php if ($project['description']): ?>
                                            <br><small class="text-muted"><?= htmlspecialchars(substr($project['description'], 0, 100)) ?>...</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge status-<?= $project['status'] ?>">
                                            <?= ucfirst(str_replace('_', ' ', $project['status'])) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?= $project['assigned_first_name'] ? htmlspecialchars($project['assigned_first_name'] . ' ' . $project['assigned_last_name']) : 'Unassigned' ?>
                                    </td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar" role="progressbar" style="width: <?= $project['progress'] ?>%">
                                                <?= $project['progress'] ?>%
                                            </div>
                                        </div>
                                    </td>
                                    <td><?= format_date($project['created_at']) ?></td>
                                    <td>
                                        <a href="../projects/view.php?id=<?= $project['id'] ?>" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-kanban" style="font-size: 3rem;"></i>
                        <p class="mt-2">No projects found for this client</p>
                        <?php if (has_permission('create_projects')): ?>
                        <a href="../projects/add.php?client_id=<?= $client['id'] ?>" class="btn btn-primary">
                            <i class="bi bi-plus"></i>
                            Create First Project
                        </a>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Quick Stats -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">Quick Stats</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary"><?= $projects->num_rows ?></h4>
                                <small class="text-muted">Projects</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success"><?= $invoices->num_rows ?></h4>
                            <small class="text-muted">Invoices</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Invoices -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">Recent Invoices</h6>
                    <?php if (has_permission('create_invoices')): ?>
                    <a href="../invoices/add.php?client_id=<?= $client['id'] ?>" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-plus"></i>
                        New Invoice
                    </a>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <?php if ($invoices->num_rows > 0): ?>
                    <div class="list-group list-group-flush">
                        <?php 
                        $invoice_count = 0;
                        while ($invoice = $invoices->fetch_assoc() && $invoice_count < 5): 
                            $invoice_count++;
                        ?>
                        <div class="list-group-item d-flex justify-content-between align-items-start px-0">
                            <div class="ms-2 me-auto">
                                <div class="fw-bold"><?= htmlspecialchars($invoice['invoice_number']) ?></div>
                                <small class="text-muted">Due: <?= format_date($invoice['due_date']) ?></small>
                            </div>
                            <div class="text-end">
                                <div class="fw-bold"><?= format_currency($invoice['total_amount']) ?></div>
                                <span class="badge status-<?= $invoice['status'] ?>">
                                    <?= ucfirst($invoice['status']) ?>
                                </span>
                            </div>
                        </div>
                        <?php endwhile; ?>
                    </div>
                    <?php if ($invoices->num_rows > 5): ?>
                    <div class="text-center mt-3">
                        <a href="../invoices/?client_id=<?= $client['id'] ?>" class="btn btn-sm btn-outline-primary">
                            View All Invoices
                        </a>
                    </div>
                    <?php endif; ?>
                    <?php else: ?>
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-receipt" style="font-size: 2rem;"></i>
                        <p class="mt-2 mb-0">No invoices yet</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
