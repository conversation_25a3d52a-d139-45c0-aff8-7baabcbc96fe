-- Initial data for Apex Company Management System
USE apex_company_management;

-- Insert default permissions
INSERT INTO permissions (name, description, module) VALUES
-- Dashboard permissions
('view_dashboard', 'View dashboard and overview', 'dashboard'),

-- User management permissions
('view_users', 'View users list', 'users'),
('create_users', 'Create new users', 'users'),
('edit_users', 'Edit user details', 'users'),
('delete_users', 'Delete users', 'users'),
('manage_roles', 'Manage user roles and permissions', 'users'),

-- Client management permissions
('view_clients', 'View clients list', 'clients'),
('create_clients', 'Create new clients', 'clients'),
('edit_clients', 'Edit client details', 'clients'),
('delete_clients', 'Delete clients', 'clients'),

-- Project management permissions
('view_projects', 'View projects list', 'projects'),
('create_projects', 'Create new projects', 'projects'),
('edit_projects', 'Edit project details', 'projects'),
('delete_projects', 'Delete projects', 'projects'),
('assign_projects', 'Assign projects to staff', 'projects'),

-- Invoice management permissions
('view_invoices', 'View invoices list', 'invoices'),
('create_invoices', 'Create new invoices', 'invoices'),
('edit_invoices', 'Edit invoice details', 'invoices'),
('delete_invoices', 'Delete invoices', 'invoices'),
('send_invoices', 'Send invoices via email', 'invoices'),

-- Payment management permissions
('view_payments', 'View payments list', 'payments'),
('create_payments', 'Record new payments', 'payments'),
('edit_payments', 'Edit payment details', 'payments'),
('delete_payments', 'Delete payment records', 'payments'),

-- File management permissions
('view_files', 'View files', 'files'),
('upload_files', 'Upload files', 'files'),
('download_files', 'Download files', 'files'),
('delete_files', 'Delete files', 'files'),

-- Communication permissions
('view_messages', 'View messages', 'communication'),
('send_messages', 'Send messages', 'communication'),
('create_groups', 'Create message groups', 'communication'),

-- Reports permissions
('view_reports', 'View reports', 'reports'),
('export_reports', 'Export reports', 'reports'),

-- Settings permissions
('view_settings', 'View system settings', 'settings'),
('edit_settings', 'Edit system settings', 'settings');

-- Assign permissions to roles
-- Super Admin gets all permissions
INSERT INTO role_permissions (role, permission_id)
SELECT 'super_admin', id FROM permissions;

-- Admin gets most permissions except user management
INSERT INTO role_permissions (role, permission_id)
SELECT 'admin', id FROM permissions 
WHERE name NOT IN ('delete_users', 'manage_roles', 'edit_settings');

-- Staff gets basic permissions
INSERT INTO role_permissions (role, permission_id)
SELECT 'staff', id FROM permissions 
WHERE name IN (
    'view_dashboard',
    'view_clients', 'create_clients', 'edit_clients',
    'view_projects', 'create_projects', 'edit_projects',
    'view_invoices', 'create_invoices', 'edit_invoices',
    'view_payments', 'create_payments',
    'view_files', 'upload_files', 'download_files',
    'view_messages', 'send_messages',
    'view_reports'
);

-- Create default super admin user
-- Password: admin123 (hashed)
INSERT INTO users (username, email, password_hash, first_name, last_name, role, status) VALUES
('admin', '<EMAIL>', '$2y$10$KkSbVP0pOBzdcLPYYNWg4.ztwhLVMjYOtGXHixJkSgDOPU6nTHXUq', 'Super', 'Admin', 'super_admin', 'active');

-- Insert default company settings
INSERT INTO company_settings (setting_key, setting_value, setting_type, description) VALUES
('company_name', 'Apex Company', 'text', 'Company name'),
('company_email', '<EMAIL>', 'text', 'Company email address'),
('company_phone', '+91-9876543210', 'text', 'Company phone number'),
('company_address', '123 Business Street, City, State - 123456', 'text', 'Company address'),
('company_logo', '', 'text', 'Company logo file path'),
('currency', 'INR', 'text', 'Default currency'),
('tax_rate', '18', 'number', 'Default GST/Tax rate'),
('invoice_prefix', 'INV-', 'text', 'Invoice number prefix'),
('invoice_terms', 'Payment due within 30 days', 'text', 'Default invoice payment terms'),
('email_notifications', '1', 'boolean', 'Enable email notifications'),
('sms_notifications', '0', 'boolean', 'Enable SMS notifications'),
('backup_frequency', 'weekly', 'text', 'Database backup frequency'),
('session_timeout', '3600', 'number', 'Session timeout in seconds'),
('max_file_size', '10485760', 'number', 'Maximum file upload size in bytes'),
('allowed_file_types', 'jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,txt', 'text', 'Allowed file extensions');
