<?php if (is_logged_in() && !isset($hide_navigation)): ?>
</div> <!-- End main-wrapper -->
<?php endif; ?>

<!-- Footer -->
<footer class="footer mt-auto py-3 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-md-6">
                <span class="text-muted">
                    &copy; <?php echo date('Y'); ?> <?php echo get_setting('company_name', APP_NAME); ?>. 
                    All rights reserved.
                </span>
            </div>
            <div class="col-md-6 text-end">
                <span class="text-muted">
                    Version <?php echo APP_VERSION; ?>
                </span>
            </div>
        </div>
    </div>
</footer>

<!-- Bootstrap 5 JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

<!-- Custom JS -->
<script src="<?php echo APP_URL; ?>assets/js/main.js"></script>

<?php if (isset($additional_js)): ?>
    <?php foreach ($additional_js as $js): ?>
        <script src="<?php echo $js; ?>"></script>
    <?php endforeach; ?>
<?php endif; ?>

<?php if (is_logged_in()): ?>
<script>
// Auto-refresh notifications and messages
$(document).ready(function() {
    // Load notifications on page load
    loadNotifications();
    loadMessageCount();
    
    // Refresh every 30 seconds
    setInterval(function() {
        loadNotifications();
        loadMessageCount();
    }, 30000);
    
    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
});

function loadNotifications() {
    $.get('<?php echo APP_URL; ?>api/notifications.php', function(data) {
        if (data.success) {
            $('#notification-count').text(data.unread_count);
            
            if (data.unread_count > 0) {
                $('#notification-count').show();
            } else {
                $('#notification-count').hide();
            }
            
            // Update dropdown content
            let dropdown = $('#notifications-dropdown');
            dropdown.empty();
            dropdown.append('<li><h6 class="dropdown-header">Notifications</h6></li>');
            
            if (data.notifications.length > 0) {
                data.notifications.forEach(function(notification) {
                    let item = `
                        <li>
                            <a class="dropdown-item ${!notification.is_read ? 'fw-bold' : ''}" 
                               href="#" onclick="markNotificationRead(${notification.id}, '${notification.action_url || '#'}')">
                                <div class="d-flex justify-content-between">
                                    <span>${notification.title}</span>
                                    <small class="text-muted">${notification.time_ago}</small>
                                </div>
                                <small class="text-muted">${notification.message}</small>
                            </a>
                        </li>
                    `;
                    dropdown.append(item);
                });
                
                dropdown.append('<li><hr class="dropdown-divider"></li>');
                dropdown.append('<li><a class="dropdown-item text-center" href="<?php echo APP_URL; ?>notifications/">View All</a></li>');
            } else {
                dropdown.append('<li><div class="dropdown-item-text text-muted">No new notifications</div></li>');
            }
        }
    });
}

function loadMessageCount() {
    $.get('<?php echo APP_URL; ?>api/messages.php?action=unread_count', function(data) {
        if (data.success) {
            $('#message-count').text(data.count);
            
            if (data.count > 0) {
                $('#message-count').show();
            } else {
                $('#message-count').hide();
            }
        }
    });
}

function markNotificationRead(id, url) {
    $.post('<?php echo APP_URL; ?>api/notifications.php', {
        action: 'mark_read',
        id: id
    }, function(data) {
        if (data.success) {
            loadNotifications();
            if (url && url !== '#') {
                window.location.href = url;
            }
        }
    });
}

// Confirm delete actions
$(document).on('click', '.btn-delete', function(e) {
    e.preventDefault();
    
    let message = $(this).data('message') || 'Are you sure you want to delete this item?';
    
    if (confirm(message)) {
        let form = $(this).closest('form');
        if (form.length) {
            form.submit();
        } else {
            window.location.href = $(this).attr('href');
        }
    }
});

// Form validation
$('.needs-validation').on('submit', function(e) {
    if (!this.checkValidity()) {
        e.preventDefault();
        e.stopPropagation();
    }
    $(this).addClass('was-validated');
});

// Auto-save drafts (for forms with class 'auto-save')
$('.auto-save').on('input change', function() {
    let form = $(this).closest('form');
    let formData = form.serialize();
    
    // Debounce auto-save
    clearTimeout(form.data('autosave-timer'));
    form.data('autosave-timer', setTimeout(function() {
        $.post('<?php echo APP_URL; ?>api/autosave.php', formData + '&action=save_draft', function(data) {
            if (data.success) {
                $('.autosave-status').text('Draft saved').fadeIn().delay(2000).fadeOut();
            }
        });
    }, 2000));
});
</script>
<?php endif; ?>

</body>
</html>
