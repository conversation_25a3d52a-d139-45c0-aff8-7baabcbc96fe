<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' . APP_NAME : APP_NAME; ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="<?php echo APP_URL; ?>assets/css/style.css" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo APP_URL; ?>assets/images/favicon.ico">
    
    <?php if (isset($additional_css)): ?>
        <?php foreach ($additional_css as $css): ?>
            <link href="<?php echo $css; ?>" rel="stylesheet">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body class="<?php echo isset($body_class) ? $body_class : ''; ?>">

<?php if (is_logged_in() && !isset($hide_navigation)): ?>
<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
    <div class="container-fluid">
        <a class="navbar-brand" href="<?php echo APP_URL; ?>dashboard/">
            <i class="bi bi-building"></i>
            <?php echo get_setting('company_name', APP_NAME); ?>
        </a>
        
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" href="<?php echo APP_URL; ?>dashboard/">
                        <i class="bi bi-speedometer2"></i> Dashboard
                    </a>
                </li>
                
                <?php if (has_permission('view_clients')): ?>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-people"></i> Clients
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="<?php echo APP_URL; ?>clients/">View All</a></li>
                        <?php if (has_permission('create_clients')): ?>
                        <li><a class="dropdown-item" href="<?php echo APP_URL; ?>clients/add.php">Add New</a></li>
                        <?php endif; ?>
                    </ul>
                </li>
                <?php endif; ?>
                
                <?php if (has_permission('view_projects')): ?>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-kanban"></i> Projects
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="<?php echo APP_URL; ?>projects/">View All</a></li>
                        <?php if (has_permission('create_projects')): ?>
                        <li><a class="dropdown-item" href="<?php echo APP_URL; ?>projects/add.php">Add New</a></li>
                        <?php endif; ?>
                    </ul>
                </li>
                <?php endif; ?>
                
                <?php if (has_permission('view_invoices')): ?>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-receipt"></i> Invoices
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="<?php echo APP_URL; ?>invoices/">View All</a></li>
                        <?php if (has_permission('create_invoices')): ?>
                        <li><a class="dropdown-item" href="<?php echo APP_URL; ?>invoices/add.php">Create New</a></li>
                        <?php endif; ?>
                    </ul>
                </li>
                <?php endif; ?>
                
                <?php if (has_permission('view_reports')): ?>
                <li class="nav-item">
                    <a class="nav-link" href="<?php echo APP_URL; ?>reports/">
                        <i class="bi bi-graph-up"></i> Reports
                    </a>
                </li>
                <?php endif; ?>
                
                <?php if (has_permission('view_files')): ?>
                <li class="nav-item">
                    <a class="nav-link" href="<?php echo APP_URL; ?>files/">
                        <i class="bi bi-folder"></i> Files
                    </a>
                </li>
                <?php endif; ?>
                
                <?php if (has_permission('view_users')): ?>
                <li class="nav-item">
                    <a class="nav-link" href="<?php echo APP_URL; ?>staff/">
                        <i class="bi bi-person-badge"></i> Staff
                    </a>
                </li>
                <?php endif; ?>
            </ul>
            
            <ul class="navbar-nav">
                <!-- Notifications -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle position-relative" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-bell"></i>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="notification-count">
                            0
                        </span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" style="width: 300px;" id="notifications-dropdown">
                        <li><h6 class="dropdown-header">Notifications</h6></li>
                        <li><div class="dropdown-item-text text-muted">No new notifications</div></li>
                    </ul>
                </li>
                
                <!-- Messages -->
                <?php if (has_permission('view_messages')): ?>
                <li class="nav-item">
                    <a class="nav-link position-relative" href="<?php echo APP_URL; ?>messages/">
                        <i class="bi bi-chat-dots"></i>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-success" id="message-count">
                            0
                        </span>
                    </a>
                </li>
                <?php endif; ?>
                
                <!-- User Menu -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle"></i>
                        <?php echo $_SESSION['user_name'] ?? 'User'; ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="<?php echo APP_URL; ?>profile/">
                            <i class="bi bi-person"></i> Profile
                        </a></li>
                        <?php if (has_permission('view_settings')): ?>
                        <li><a class="dropdown-item" href="<?php echo APP_URL; ?>settings/">
                            <i class="bi bi-gear"></i> Settings
                        </a></li>
                        <?php endif; ?>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="<?php echo APP_URL; ?>auth/logout.php">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<!-- Main Content Wrapper -->
<div class="main-wrapper">
<?php endif; ?>

<!-- Alert Messages -->
<?php if (isset($_SESSION['success'])): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i>
        <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($_SESSION['error'])): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i>
        <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($_SESSION['warning'])): ?>
    <div class="alert alert-warning alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i>
        <?php echo $_SESSION['warning']; unset($_SESSION['warning']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($_SESSION['info'])): ?>
    <div class="alert alert-info alert-dismissible fade show" role="alert">
        <i class="bi bi-info-circle"></i>
        <?php echo $_SESSION['info']; unset($_SESSION['info']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>
