<?php
/**
 * Apex Company Management System
 * Middleware Functions
 */

if (!defined('APEX_ACCESS')) {
    die('Direct access not permitted');
}

/**
 * Authentication middleware
 */
function auth_middleware() {
    if (!is_logged_in()) {
        $_SESSION['error'] = 'Please login to access this page';
        header('Location: ' . APP_URL . 'auth/login.php');
        exit();
    }
    
    // Validate session
    if (!validate_user_session()) {
        $_SESSION['error'] = 'Your session has expired. Please login again.';
        header('Location: ' . APP_URL . 'auth/login.php');
        exit();
    }
}

/**
 * Permission middleware
 */
function permission_middleware($permission) {
    auth_middleware();
    require_permission($permission);
}

/**
 * CSRF middleware
 */
function csrf_middleware() {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $token = $_POST['csrf_token'] ?? '';
        
        if (!verify_csrf_token($token)) {
            $_SESSION['error'] = 'Invalid security token. Please try again.';
            header('Location: ' . $_SERVER['HTTP_REFERER'] ?? APP_URL);
            exit();
        }
    }
}

/**
 * Rate limiting middleware
 */
function rate_limit_middleware($action, $limit = 10, $window = 60) {
    if (!check_rate_limit($action, $limit, $window)) {
        http_response_code(429);
        $_SESSION['error'] = 'Too many requests. Please try again later.';
        header('Location: ' . $_SERVER['HTTP_REFERER'] ?? APP_URL);
        exit();
    }
}

/**
 * Input validation middleware
 */
function validate_input_middleware($rules) {
    $errors = [];
    
    foreach ($rules as $field => $rule) {
        $value = $_POST[$field] ?? '';
        
        // Required validation
        if (isset($rule['required']) && $rule['required'] && empty($value)) {
            $errors[] = ($rule['label'] ?? $field) . ' is required';
            continue;
        }
        
        // Skip other validations if field is empty and not required
        if (empty($value) && !isset($rule['required'])) {
            continue;
        }
        
        // Email validation
        if (isset($rule['email']) && $rule['email'] && !validate_email($value)) {
            $errors[] = ($rule['label'] ?? $field) . ' must be a valid email address';
        }
        
        // Minimum length validation
        if (isset($rule['min_length']) && strlen($value) < $rule['min_length']) {
            $errors[] = ($rule['label'] ?? $field) . ' must be at least ' . $rule['min_length'] . ' characters';
        }
        
        // Maximum length validation
        if (isset($rule['max_length']) && strlen($value) > $rule['max_length']) {
            $errors[] = ($rule['label'] ?? $field) . ' must not exceed ' . $rule['max_length'] . ' characters';
        }
        
        // Numeric validation
        if (isset($rule['numeric']) && $rule['numeric'] && !is_numeric($value)) {
            $errors[] = ($rule['label'] ?? $field) . ' must be a number';
        }
        
        // Custom validation
        if (isset($rule['custom']) && is_callable($rule['custom'])) {
            $custom_result = $rule['custom']($value);
            if ($custom_result !== true) {
                $errors[] = $custom_result;
            }
        }
    }
    
    if (!empty($errors)) {
        $_SESSION['error'] = implode('<br>', $errors);
        header('Location: ' . $_SERVER['HTTP_REFERER'] ?? APP_URL);
        exit();
    }
}

/**
 * File upload middleware
 */
function file_upload_middleware($file_field, $options = []) {
    if (!isset($_FILES[$file_field])) {
        return null;
    }
    
    $file = $_FILES[$file_field];
    $validation = sanitize_file_upload($file);
    
    if (!$validation['valid']) {
        $_SESSION['error'] = implode('<br>', $validation['errors']);
        header('Location: ' . $_SERVER['HTTP_REFERER'] ?? APP_URL);
        exit();
    }
    
    return $validation['file_info'];
}

/**
 * JSON response middleware
 */
function json_response($data, $status_code = 200) {
    http_response_code($status_code);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit();
}

/**
 * Pagination middleware
 */
function pagination_middleware($total_records, $per_page = 25) {
    $page = max(1, (int)($_GET['page'] ?? 1));
    $offset = ($page - 1) * $per_page;
    $total_pages = ceil($total_records / $per_page);
    
    return [
        'page' => $page,
        'per_page' => $per_page,
        'offset' => $offset,
        'total_pages' => $total_pages,
        'total_records' => $total_records,
        'has_prev' => $page > 1,
        'has_next' => $page < $total_pages,
        'prev_page' => $page > 1 ? $page - 1 : null,
        'next_page' => $page < $total_pages ? $page + 1 : null
    ];
}

/**
 * Search middleware
 */
function search_middleware($search_fields = []) {
    $search = sanitize_input($_GET['search'] ?? '');
    $search_conditions = [];
    $search_params = [];
    
    if (!empty($search) && !empty($search_fields)) {
        $conditions = [];
        foreach ($search_fields as $field) {
            $conditions[] = "$field LIKE ?";
            $search_params[] = "%$search%";
        }
        $search_conditions = [
            'sql' => '(' . implode(' OR ', $conditions) . ')',
            'params' => $search_params
        ];
    }
    
    return [
        'search' => $search,
        'conditions' => $search_conditions
    ];
}

/**
 * Sort middleware
 */
function sort_middleware($allowed_columns = [], $default_column = 'id', $default_direction = 'DESC') {
    $sort_column = $_GET['sort'] ?? $default_column;
    $sort_direction = strtoupper($_GET['direction'] ?? $default_direction);
    
    // Validate sort column
    if (!in_array($sort_column, $allowed_columns)) {
        $sort_column = $default_column;
    }
    
    // Validate sort direction
    if (!in_array($sort_direction, ['ASC', 'DESC'])) {
        $sort_direction = $default_direction;
    }
    
    return [
        'column' => $sort_column,
        'direction' => $sort_direction,
        'sql' => "ORDER BY $sort_column $sort_direction"
    ];
}

/**
 * Filter middleware
 */
function filter_middleware($filters = []) {
    $active_filters = [];
    $filter_conditions = [];
    $filter_params = [];
    
    foreach ($filters as $filter_name => $filter_config) {
        $value = $_GET[$filter_name] ?? '';
        
        if (!empty($value)) {
            $active_filters[$filter_name] = $value;
            
            if (isset($filter_config['sql'])) {
                $filter_conditions[] = $filter_config['sql'];
                
                if (isset($filter_config['params'])) {
                    if (is_array($filter_config['params'])) {
                        $filter_params = array_merge($filter_params, $filter_config['params']);
                    } else {
                        $filter_params[] = $value;
                    }
                } else {
                    $filter_params[] = $value;
                }
            }
        }
    }
    
    return [
        'active' => $active_filters,
        'conditions' => [
            'sql' => !empty($filter_conditions) ? implode(' AND ', $filter_conditions) : '',
            'params' => $filter_params
        ]
    ];
}

/**
 * Cache middleware
 */
function cache_middleware($key, $ttl = 3600) {
    // Simple file-based caching
    $cache_dir = __DIR__ . '/../cache/';
    if (!is_dir($cache_dir)) {
        mkdir($cache_dir, 0755, true);
    }
    
    $cache_file = $cache_dir . md5($key) . '.cache';
    
    // Check if cache exists and is valid
    if (file_exists($cache_file) && (time() - filemtime($cache_file)) < $ttl) {
        return unserialize(file_get_contents($cache_file));
    }
    
    return null;
}

/**
 * Set cache
 */
function set_cache($key, $data) {
    $cache_dir = __DIR__ . '/../cache/';
    if (!is_dir($cache_dir)) {
        mkdir($cache_dir, 0755, true);
    }
    
    $cache_file = $cache_dir . md5($key) . '.cache';
    file_put_contents($cache_file, serialize($data));
}

/**
 * Clear cache
 */
function clear_cache($pattern = '*') {
    $cache_dir = __DIR__ . '/../cache/';
    $files = glob($cache_dir . $pattern . '.cache');
    
    foreach ($files as $file) {
        unlink($file);
    }
}
?>
