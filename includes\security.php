<?php
/**
 * Apex Company Management System
 * Security Functions
 */

if (!defined('APEX_ACCESS')) {
    die('Direct access not permitted');
}

/**
 * Generate CSRF token
 */
function generate_csrf_token() {
    if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time']) || 
        (time() - $_SESSION['csrf_token_time']) > CSRF_TOKEN_EXPIRE) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        $_SESSION['csrf_token_time'] = time();
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 */
function verify_csrf_token($token) {
    if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time'])) {
        return false;
    }
    
    if ((time() - $_SESSION['csrf_token_time']) > CSRF_TOKEN_EXPIRE) {
        unset($_SESSION['csrf_token'], $_SESSION['csrf_token_time']);
        return false;
    }
    
    return hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Get CSRF token input field
 */
function csrf_token_input() {
    $token = generate_csrf_token();
    return '<input type="hidden" name="csrf_token" value="' . $token . '">';
}



/**
 * Validate password strength
 */
function validate_password_strength($password) {
    $errors = [];
    
    if (strlen($password) < PASSWORD_MIN_LENGTH) {
        $errors[] = "Password must be at least " . PASSWORD_MIN_LENGTH . " characters long";
    }
    
    if (!preg_match('/[A-Z]/', $password)) {
        $errors[] = "Password must contain at least one uppercase letter";
    }
    
    if (!preg_match('/[a-z]/', $password)) {
        $errors[] = "Password must contain at least one lowercase letter";
    }
    
    if (!preg_match('/[0-9]/', $password)) {
        $errors[] = "Password must contain at least one number";
    }
    
    if (!preg_match('/[^A-Za-z0-9]/', $password)) {
        $errors[] = "Password must contain at least one special character";
    }
    
    return $errors;
}

/**
 * Clean session data
 */
function clean_session() {
    global $mysqli;
    
    // Remove expired sessions from database
    $stmt = $mysqli->prepare("DELETE FROM user_sessions WHERE last_activity < DATE_SUB(NOW(), INTERVAL ? SECOND)");
    $stmt->bind_param("i", SESSION_TIMEOUT);
    $stmt->execute();
}



/**
 * Validate user session
 */
function validate_user_session() {
    global $mysqli;
    
    if (!is_logged_in()) {
        return false;
    }
    
    $session_id = session_id();
    $user_id = $_SESSION['user_id'];
    
    $stmt = $mysqli->prepare("
        SELECT us.*, u.status 
        FROM user_sessions us 
        JOIN users u ON us.user_id = u.id 
        WHERE us.id = ? AND us.user_id = ? AND u.status = 'active'
    ");
    $stmt->bind_param("si", $session_id, $user_id);
    $stmt->execute();
    $result = $stmt->get_result()->fetch_assoc();
    
    if (!$result) {
        session_destroy();
        return false;
    }
    
    // Check session timeout
    if (strtotime($result['last_activity']) < (time() - SESSION_TIMEOUT)) {
        // Remove expired session
        $stmt = $mysqli->prepare("DELETE FROM user_sessions WHERE user_id = ?");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        session_destroy();
        return false;
    }
    
    // Update last activity
    $stmt = $mysqli->prepare("UPDATE user_sessions SET last_activity = NOW() WHERE id = ?");
    $stmt->bind_param("s", $session_id);
    $stmt->execute();
    
    return true;
}



/**
 * Sanitize file upload
 */
function sanitize_file_upload($file) {
    $errors = [];
    
    // Check if file was uploaded
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        $errors[] = 'No file uploaded or upload error';
        return ['valid' => false, 'errors' => $errors];
    }
    
    // Check file size
    if ($file['size'] > MAX_FILE_SIZE) {
        $errors[] = 'File size exceeds maximum allowed size of ' . format_file_size(MAX_FILE_SIZE);
    }
    
    // Check file type
    if (!is_allowed_file_type($file['name'])) {
        $errors[] = 'File type not allowed';
    }
    
    // Check for malicious content (basic check)
    $file_content = file_get_contents($file['tmp_name']);
    if (strpos($file_content, '<?php') !== false || strpos($file_content, '<script') !== false) {
        $errors[] = 'File contains potentially malicious content';
    }
    
    return [
        'valid' => empty($errors),
        'errors' => $errors,
        'file_info' => [
            'name' => $file['name'],
            'size' => $file['size'],
            'type' => $file['type'],
            'tmp_name' => $file['tmp_name']
        ]
    ];
}

/**
 * Rate limiting
 */
function check_rate_limit($action, $limit = 10, $window = 60) {
    $key = $action . '_' . ($_SERVER['REMOTE_ADDR'] ?? 'unknown');
    
    if (!isset($_SESSION['rate_limits'])) {
        $_SESSION['rate_limits'] = [];
    }
    
    $now = time();
    
    // Clean old entries
    foreach ($_SESSION['rate_limits'] as $k => $data) {
        if ($data['time'] < ($now - $window)) {
            unset($_SESSION['rate_limits'][$k]);
        }
    }
    
    // Count current requests
    $count = 0;
    foreach ($_SESSION['rate_limits'] as $data) {
        if ($data['action'] === $action && $data['time'] >= ($now - $window)) {
            $count++;
        }
    }
    
    if ($count >= $limit) {
        return false;
    }
    
    // Record this request
    $_SESSION['rate_limits'][] = [
        'action' => $action,
        'time' => $now
    ];
    
    return true;
}
?>
