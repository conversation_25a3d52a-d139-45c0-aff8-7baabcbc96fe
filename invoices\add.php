<?php
/**
 * Apex Company Management System
 * Add Invoice
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';
require_once '../includes/middleware.php';

// Check authentication and permission
auth_middleware();
permission_middleware('create_invoices');

$error = '';
$success = '';

// Get pre-selected client and project if coming from other pages
$selected_client = (int)($_GET['client_id'] ?? 0);
$selected_project = (int)($_GET['project_id'] ?? 0);

// Handle form submission
if ($_POST) {
    if (!verify_csrf_token($_POST['csrf_token'])) {
        $error = 'Invalid security token. Please try again.';
    } else {
        $client_id = (int)($_POST['client_id'] ?? 0);
        $project_id = (int)($_POST['project_id'] ?? 0);
        $issue_date = $_POST['issue_date'] ?? '';
        $due_date = $_POST['due_date'] ?? '';
        $notes = trim($_POST['notes'] ?? '');
        $terms = trim($_POST['terms'] ?? '');
        $tax_rate = (float)($_POST['tax_rate'] ?? 0);
        $discount_amount = (float)($_POST['discount_amount'] ?? 0);
        
        // Invoice items
        $items = $_POST['items'] ?? [];
        
        // Validation
        if (!$client_id) {
            $error = 'Please select a client.';
        } elseif (empty($issue_date)) {
            $error = 'Issue date is required.';
        } elseif (empty($items) || !is_array($items)) {
            $error = 'Please add at least one invoice item.';
        } else {
            // Validate items
            $valid_items = [];
            $subtotal = 0;
            
            foreach ($items as $item) {
                $description = trim($item['description'] ?? '');
                $quantity = (float)($item['quantity'] ?? 0);
                $rate = (float)($item['rate'] ?? 0);
                
                if (!empty($description) && $quantity > 0 && $rate > 0) {
                    $amount = $quantity * $rate;
                    $valid_items[] = [
                        'description' => $description,
                        'quantity' => $quantity,
                        'rate' => $rate,
                        'amount' => $amount
                    ];
                    $subtotal += $amount;
                }
            }
            
            if (empty($valid_items)) {
                $error = 'Please add at least one valid invoice item.';
            } else {
                // Calculate totals
                $tax_amount = ($subtotal * $tax_rate) / 100;
                $total_amount = $subtotal + $tax_amount - $discount_amount;
                
                // Generate invoice number
                $year = date('Y');
                $stmt = $mysqli->prepare("SELECT COUNT(*) as count FROM invoices WHERE YEAR(created_at) = ?");
                $stmt->bind_param("i", $year);
                $stmt->execute();
                $count = $stmt->get_result()->fetch_assoc()['count'];
                $invoice_number = 'INV-' . $year . '-' . str_pad($count + 1, 4, '0', STR_PAD_LEFT);
                
                // Insert invoice
                $stmt = $mysqli->prepare("
                    INSERT INTO invoices (invoice_number, client_id, project_id, issue_date, due_date, 
                                        subtotal, tax_rate, tax_amount, discount_amount, total_amount, 
                                        notes, terms, status, created_by, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'draft', ?, NOW())
                ");
                
                $due_date_value = !empty($due_date) ? $due_date : null;
                $project_id_value = $project_id > 0 ? $project_id : null;
                
                $stmt->bind_param("siissddddsssi", 
                    $invoice_number, $client_id, $project_id_value, $issue_date, $due_date_value,
                    $subtotal, $tax_rate, $tax_amount, $discount_amount, $total_amount,
                    $notes, $terms, $_SESSION['user_id']
                );
                
                if ($stmt->execute()) {
                    $invoice_id = $mysqli->insert_id;
                    
                    // Insert invoice items
                    $stmt = $mysqli->prepare("
                        INSERT INTO invoice_items (invoice_id, description, quantity, rate, amount) 
                        VALUES (?, ?, ?, ?, ?)
                    ");
                    
                    foreach ($valid_items as $item) {
                        $stmt->bind_param("isddd", 
                            $invoice_id, $item['description'], $item['quantity'], 
                            $item['rate'], $item['amount']
                        );
                        $stmt->execute();
                    }
                    
                    log_activity('Invoice Created', 'invoices', $invoice_id);
                    $_SESSION['success'] = 'Invoice created successfully!';
                    header('Location: view.php?id=' . $invoice_id);
                    exit();
                } else {
                    $error = 'Error creating invoice. Please try again.';
                }
            }
        }
    }
}

// Get clients for dropdown
$clients_query = "SELECT id, company_name FROM clients WHERE status = 'active' ORDER BY company_name";
$clients_result = $mysqli->query($clients_query);

// Get projects for dropdown
$projects_query = "
    SELECT p.id, p.project_name, c.company_name 
    FROM projects p 
    LEFT JOIN clients c ON p.client_id = c.id 
    WHERE p.status NOT IN ('completed', 'cancelled') 
    ORDER BY p.project_name
";
$projects_result = $mysqli->query($projects_query);

$page_title = 'Create Invoice';
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-plus-square"></i>
                    Create Invoice
                </h1>
                <a href="index.php" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i>
                    Back to Invoices
                </a>
            </div>
        </div>
    </div>

    <?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <?= htmlspecialchars($error) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <form method="POST" action="" id="invoiceForm">
        <?= csrf_token_input() ?>
        
        <div class="row">
            <div class="col-lg-8">
                <!-- Invoice Details -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Invoice Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="client_id" class="form-label">Client <span class="text-danger">*</span></label>
                                <select class="form-select" id="client_id" name="client_id" required>
                                    <option value="">Select Client</option>
                                    <?php while ($client = $clients_result->fetch_assoc()): ?>
                                    <option value="<?= $client['id'] ?>" 
                                            <?= ($selected_client == $client['id'] || ($_POST['client_id'] ?? 0) == $client['id']) ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($client['company_name']) ?>
                                    </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="project_id" class="form-label">Project (Optional)</label>
                                <select class="form-select" id="project_id" name="project_id">
                                    <option value="">Select Project</option>
                                    <?php while ($project = $projects_result->fetch_assoc()): ?>
                                    <option value="<?= $project['id'] ?>" 
                                            <?= ($selected_project == $project['id'] || ($_POST['project_id'] ?? 0) == $project['id']) ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($project['project_name']) ?>
                                        <?php if ($project['company_name']): ?>
                                            (<?= htmlspecialchars($project['company_name']) ?>)
                                        <?php endif; ?>
                                    </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="issue_date" class="form-label">Issue Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="issue_date" name="issue_date" 
                                       value="<?= $_POST['issue_date'] ?? date('Y-m-d') ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="due_date" class="form-label">Due Date</label>
                                <input type="date" class="form-control" id="due_date" name="due_date" 
                                       value="<?= $_POST['due_date'] ?? date('Y-m-d', strtotime('+30 days')) ?>">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Invoice Items -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Invoice Items</h5>
                        <button type="button" class="btn btn-sm btn-primary" onclick="addInvoiceItem()">
                            <i class="bi bi-plus"></i>
                            Add Item
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="invoiceItems">
                            <!-- Items will be added here dynamically -->
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-md-6 offset-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-bold">Subtotal:</td>
                                        <td class="text-end" id="subtotalDisplay">₹0.00</td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <label for="tax_rate">Tax Rate (%):</label>
                                            <input type="number" class="form-control form-control-sm" 
                                                   id="tax_rate" name="tax_rate" value="<?= $_POST['tax_rate'] ?? 18 ?>" 
                                                   step="0.01" min="0" max="100" onchange="calculateTotals()">
                                        </td>
                                        <td class="text-end" id="taxDisplay">₹0.00</td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <label for="discount_amount">Discount:</label>
                                            <input type="number" class="form-control form-control-sm" 
                                                   id="discount_amount" name="discount_amount" value="<?= $_POST['discount_amount'] ?? 0 ?>" 
                                                   step="0.01" min="0" onchange="calculateTotals()">
                                        </td>
                                        <td class="text-end text-danger" id="discountDisplay">-₹0.00</td>
                                    </tr>
                                    <tr class="border-top">
                                        <td class="fw-bold h5">Total:</td>
                                        <td class="text-end fw-bold h5" id="totalDisplay">₹0.00</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Additional Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3" 
                                      placeholder="Any additional notes for the client..."><?= htmlspecialchars($_POST['notes'] ?? '') ?></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="terms" class="form-label">Terms & Conditions</label>
                            <textarea class="form-control" id="terms" name="terms" rows="3" 
                                      placeholder="Payment terms and conditions..."><?= htmlspecialchars($_POST['terms'] ?? 'Payment is due within 30 days of invoice date.') ?></textarea>
                        </div>
                    </div>
                </div>

                <div class="d-flex gap-2 mb-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check"></i>
                        Create Invoice
                    </button>
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="bi bi-x"></i>
                        Cancel
                    </a>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Tips</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled small text-muted">
                            <li><i class="bi bi-info-circle text-primary"></i> Select a client to generate the invoice.</li>
                            <li><i class="bi bi-info-circle text-primary"></i> Link to a project for better tracking.</li>
                            <li><i class="bi bi-info-circle text-primary"></i> Add detailed descriptions for each item.</li>
                            <li><i class="bi bi-info-circle text-primary"></i> Set appropriate tax rates and discounts.</li>
                            <li><i class="bi bi-info-circle text-primary"></i> Include clear payment terms.</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
let itemCounter = 0;

function addInvoiceItem(description = '', quantity = 1, rate = 0) {
    itemCounter++;
    const itemHtml = `
        <div class="invoice-item border rounded p-3 mb-3" id="item-${itemCounter}">
            <div class="row">
                <div class="col-md-5 mb-2">
                    <label class="form-label">Description</label>
                    <input type="text" class="form-control" name="items[${itemCounter}][description]" 
                           value="${description}" placeholder="Item description" required>
                </div>
                <div class="col-md-2 mb-2">
                    <label class="form-label">Quantity</label>
                    <input type="number" class="form-control" name="items[${itemCounter}][quantity]" 
                           value="${quantity}" step="0.01" min="0.01" onchange="calculateItemAmount(${itemCounter})" required>
                </div>
                <div class="col-md-2 mb-2">
                    <label class="form-label">Rate</label>
                    <input type="number" class="form-control" name="items[${itemCounter}][rate]" 
                           value="${rate}" step="0.01" min="0.01" onchange="calculateItemAmount(${itemCounter})" required>
                </div>
                <div class="col-md-2 mb-2">
                    <label class="form-label">Amount</label>
                    <input type="text" class="form-control" id="amount-${itemCounter}" readonly>
                </div>
                <div class="col-md-1 mb-2">
                    <label class="form-label">&nbsp;</label>
                    <button type="button" class="btn btn-outline-danger btn-sm w-100" onclick="removeInvoiceItem(${itemCounter})">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('invoiceItems').insertAdjacentHTML('beforeend', itemHtml);
    calculateItemAmount(itemCounter);
}

function removeInvoiceItem(itemId) {
    document.getElementById(`item-${itemId}`).remove();
    calculateTotals();
}

function calculateItemAmount(itemId) {
    const quantity = parseFloat(document.querySelector(`input[name="items[${itemId}][quantity]"]`).value) || 0;
    const rate = parseFloat(document.querySelector(`input[name="items[${itemId}][rate]"]`).value) || 0;
    const amount = quantity * rate;
    
    document.getElementById(`amount-${itemId}`).value = '₹' + amount.toFixed(2);
    calculateTotals();
}

function calculateTotals() {
    let subtotal = 0;
    
    // Calculate subtotal from all items
    document.querySelectorAll('.invoice-item').forEach(item => {
        const quantity = parseFloat(item.querySelector('input[name*="[quantity]"]').value) || 0;
        const rate = parseFloat(item.querySelector('input[name*="[rate]"]').value) || 0;
        subtotal += quantity * rate;
    });
    
    const taxRate = parseFloat(document.getElementById('tax_rate').value) || 0;
    const discountAmount = parseFloat(document.getElementById('discount_amount').value) || 0;
    
    const taxAmount = (subtotal * taxRate) / 100;
    const total = subtotal + taxAmount - discountAmount;
    
    document.getElementById('subtotalDisplay').textContent = '₹' + subtotal.toFixed(2);
    document.getElementById('taxDisplay').textContent = '₹' + taxAmount.toFixed(2);
    document.getElementById('discountDisplay').textContent = '-₹' + discountAmount.toFixed(2);
    document.getElementById('totalDisplay').textContent = '₹' + total.toFixed(2);
}

// Add initial item
document.addEventListener('DOMContentLoaded', function() {
    addInvoiceItem();
});
</script>

<?php require_once '../includes/footer.php'; ?>
