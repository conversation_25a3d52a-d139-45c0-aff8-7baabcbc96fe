<?php
/**
 * Apex Company Management System
 * Invoices List
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';
require_once '../includes/middleware.php';

// Check authentication and permission
auth_middleware();
permission_middleware('view_invoices');

// Handle search and filters
$search = sanitize_input($_GET['search'] ?? '');
$status_filter = $_GET['status'] ?? '';
$client_filter = (int)($_GET['client_id'] ?? 0);

// Build query conditions
$where_conditions = [];
$params = [];
$param_types = '';

if (!empty($search)) {
    $where_conditions[] = "(i.invoice_number LIKE ? OR c.company_name LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $param_types .= 'ss';
}

if (!empty($status_filter)) {
    $where_conditions[] = "i.status = ?";
    $params[] = $status_filter;
    $param_types .= 's';
}

if ($client_filter > 0) {
    $where_conditions[] = "i.client_id = ?";
    $params[] = $client_filter;
    $param_types .= 'i';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get invoices with client info
$query = "
    SELECT i.*,
           c.company_name as client_name,
           p.project_name
    FROM invoices i
    LEFT JOIN clients c ON i.client_id = c.id
    LEFT JOIN projects p ON i.project_id = p.id
    $where_clause
    ORDER BY i.created_at DESC
";

$stmt = $mysqli->prepare($query);
if (!empty($params)) {
    $stmt->bind_param($param_types, ...$params);
}
$stmt->execute();
$invoices = $stmt->get_result();

// Get clients for filter dropdown
$clients_query = "SELECT id, company_name FROM clients WHERE status = 'active' ORDER BY company_name";
$clients_result = $mysqli->query($clients_query);

// Calculate totals
$totals_query = "
    SELECT
        COUNT(*) as total_invoices,
        SUM(CASE WHEN status = 'draft' THEN 1 ELSE 0 END) as draft_count,
        SUM(CASE WHEN status = 'sent' THEN 1 ELSE 0 END) as sent_count,
        SUM(CASE WHEN status = 'paid' THEN 1 ELSE 0 END) as paid_count,
        SUM(CASE WHEN status = 'overdue' THEN 1 ELSE 0 END) as overdue_count,
        SUM(total_amount) as total_amount,
        SUM(CASE WHEN status = 'paid' THEN total_amount ELSE 0 END) as paid_amount,
        SUM(CASE WHEN status IN ('sent', 'overdue') THEN total_amount ELSE 0 END) as pending_amount
    FROM invoices
";
$totals = $mysqli->query($totals_query)->fetch_assoc();

$page_title = 'Invoices';
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-receipt"></i>
                    Invoices
                </h1>
                <?php if (has_permission('create_invoices')): ?>
                <a href="add.php" class="btn btn-primary">
                    <i class="bi bi-plus"></i>
                    Create Invoice
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= $totals['total_invoices'] ?></h4>
                            <p class="mb-0">Total Invoices</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-receipt" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= format_currency($totals['paid_amount']) ?></h4>
                            <p class="mb-0">Paid Amount</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= format_currency($totals['pending_amount']) ?></h4>
                            <p class="mb-0">Pending Amount</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-clock" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= $totals['overdue_count'] ?></h4>
                            <p class="mb-0">Overdue Invoices</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-exclamation-triangle" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search"
                                   value="<?= htmlspecialchars($search) ?>" placeholder="Search invoices...">
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">All Statuses</option>
                                <option value="draft" <?= $status_filter === 'draft' ? 'selected' : '' ?>>Draft</option>
                                <option value="sent" <?= $status_filter === 'sent' ? 'selected' : '' ?>>Sent</option>
                                <option value="paid" <?= $status_filter === 'paid' ? 'selected' : '' ?>>Paid</option>
                                <option value="overdue" <?= $status_filter === 'overdue' ? 'selected' : '' ?>>Overdue</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="client_id" class="form-label">Client</label>
                            <select class="form-select" id="client_id" name="client_id">
                                <option value="">All Clients</option>
                                <?php while ($client = $clients_result->fetch_assoc()): ?>
                                <option value="<?= $client['id'] ?>" <?= $client_filter == $client['id'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($client['company_name']) ?>
                                </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-search"></i>
                                </button>
                                <a href="index.php" class="btn btn-outline-secondary">
                                    <i class="bi bi-x"></i>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">All Invoices</h5>
                        <div class="d-flex gap-2">
                            <span class="badge bg-secondary"><?= $totals['draft_count'] ?> Draft</span>
                            <span class="badge bg-primary"><?= $totals['sent_count'] ?> Sent</span>
                            <span class="badge bg-success"><?= $totals['paid_count'] ?> Paid</span>
                            <span class="badge bg-danger"><?= $totals['overdue_count'] ?> Overdue</span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <?php if ($invoices->num_rows > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Invoice #</th>
                                    <th>Client</th>
                                    <th>Project</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Issue Date</th>
                                    <th>Due Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($invoice = $invoices->fetch_assoc()): ?>
                                <tr>
                                    <td>
                                        <strong><?= htmlspecialchars($invoice['invoice_number']) ?></strong>
                                    </td>
                                    <td>
                                        <?php if ($invoice['client_name']): ?>
                                            <a href="../clients/view.php?id=<?= $invoice['client_id'] ?>" class="text-decoration-none">
                                                <?= htmlspecialchars($invoice['client_name']) ?>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">No Client</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($invoice['project_name']): ?>
                                            <a href="../projects/view.php?id=<?= $invoice['project_id'] ?>" class="text-decoration-none">
                                                <?= htmlspecialchars($invoice['project_name']) ?>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">No Project</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong><?= format_currency($invoice['total_amount']) ?></strong>
                                    </td>
                                    <td>
                                        <span class="badge status-<?= $invoice['status'] ?>">
                                            <?= ucfirst($invoice['status']) ?>
                                        </span>
                                    </td>
                                    <td><?= format_date($invoice['issue_date']) ?></td>
                                    <td>
                                        <?php if ($invoice['due_date']): ?>
                                            <span class="<?= strtotime($invoice['due_date']) < time() && $invoice['status'] !== 'paid' ? 'text-danger' : '' ?>">
                                                <?= format_date($invoice['due_date']) ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">No due date</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="view.php?id=<?= $invoice['id'] ?>" class="btn btn-outline-primary" title="View">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <?php if (has_permission('edit_invoices')): ?>
                                            <a href="edit.php?id=<?= $invoice['id'] ?>" class="btn btn-outline-warning" title="Edit">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <?php endif; ?>
                                            <a href="pdf.php?id=<?= $invoice['id'] ?>" class="btn btn-outline-info" title="Download PDF" target="_blank">
                                                <i class="bi bi-file-pdf"></i>
                                            </a>
                                            <?php if (has_permission('delete_invoices')): ?>
                                            <button type="button" class="btn btn-outline-danger" title="Delete"
                                                    onclick="deleteInvoice(<?= $invoice['id'] ?>, '<?= htmlspecialchars($invoice['invoice_number']) ?>')">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="text-center text-muted py-5">
                        <i class="bi bi-receipt" style="font-size: 4rem;"></i>
                        <h4 class="mt-3">No Invoices Found</h4>
                        <p>No invoices match your current filters.</p>
                        <?php if (has_permission('create_invoices')): ?>
                        <a href="add.php" class="btn btn-primary mt-3">
                            <i class="bi bi-plus"></i>
                            Create First Invoice
                        </a>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if (has_permission('delete_invoices')): ?>
<script>
function deleteInvoice(id, number) {
    if (confirm('Are you sure you want to delete invoice "' + number + '"? This action cannot be undone.')) {
        fetch('delete.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'id=' + id + '&csrf_token=' + '<?= generate_csrf_token() ?>'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error deleting invoice');
        });
    }
}
</script>
<?php endif; ?>

<?php require_once '../includes/footer.php'; ?>
