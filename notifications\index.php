<?php
/**
 * Apex Company Management System
 * Notifications
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';
require_once '../includes/middleware.php';

// Check authentication and permission
auth_middleware();
permission_middleware('view_notifications');

$page_title = 'Notifications';
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-bell"></i>
                    Notifications
                </h1>
                <button class="btn btn-outline-primary" onclick="markAllNotificationsRead()">
                    <i class="bi bi-check-all"></i>
                    Mark All Read
                </button>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="text-center text-muted py-5">
                        <i class="bi bi-bell" style="font-size: 4rem;"></i>
                        <h4 class="mt-3">Notifications</h4>
                        <p>This module is under development.</p>
                        <p>You will receive system notifications and alerts here.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
