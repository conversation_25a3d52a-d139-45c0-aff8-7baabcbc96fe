<?php
/**
 * Apex Company Management System
 * Delete Project
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';
require_once '../includes/middleware.php';

// Check authentication and permission
auth_middleware();
permission_middleware('delete_projects');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit();
}

if (!verify_csrf_token($_POST['csrf_token'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid security token']);
    exit();
}

$project_id = (int)($_POST['id'] ?? 0);

if (!$project_id) {
    echo json_encode(['success' => false, 'message' => 'Invalid project ID']);
    exit();
}

// Check if project exists
$stmt = $mysqli->prepare("SELECT project_name FROM projects WHERE id = ?");
$stmt->bind_param("i", $project_id);
$stmt->execute();
$result = $stmt->get_result();
$project = $result->fetch_assoc();

if (!$project) {
    echo json_encode(['success' => false, 'message' => 'Project not found']);
    exit();
}

// Check for related records
$checks = [
    'project_tasks' => 'tasks',
    'project_milestones' => 'milestones',
    'invoices' => 'invoices'
];

$related_records = [];
foreach ($checks as $table => $label) {
    $stmt = $mysqli->prepare("SELECT COUNT(*) as count FROM $table WHERE project_id = ?");
    $stmt->bind_param("i", $project_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $count = $result->fetch_assoc()['count'];
    
    if ($count > 0) {
        $related_records[] = "$count $label";
    }
}

if (!empty($related_records)) {
    $message = 'Cannot delete project. It has associated ' . implode(', ', $related_records) . '. Please delete or reassign these records first.';
    echo json_encode(['success' => false, 'message' => $message]);
    exit();
}

// Delete the project
$stmt = $mysqli->prepare("DELETE FROM projects WHERE id = ?");
$stmt->bind_param("i", $project_id);

if ($stmt->execute()) {
    log_activity('Project Deleted', 'projects', $project_id, $project['project_name']);
    echo json_encode(['success' => true, 'message' => 'Project deleted successfully']);
} else {
    echo json_encode(['success' => false, 'message' => 'Error deleting project']);
}
?>
