<?php
/**
 * Apex Company Management System
 * Edit Project
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';
require_once '../includes/middleware.php';

// Check authentication and permission
auth_middleware();
permission_middleware('edit_projects');

$project_id = (int)($_GET['id'] ?? 0);
$error = '';
$success = '';

if (!$project_id) {
    $_SESSION['error'] = 'Invalid project ID.';
    header('Location: index.php');
    exit();
}

// Get project details
$stmt = $mysqli->prepare("SELECT * FROM projects WHERE id = ?");
$stmt->bind_param("i", $project_id);
$stmt->execute();
$result = $stmt->get_result();
$project = $result->fetch_assoc();

if (!$project) {
    $_SESSION['error'] = 'Project not found.';
    header('Location: index.php');
    exit();
}

// Handle form submission
if ($_POST) {
    if (!verify_csrf_token($_POST['csrf_token'])) {
        $error = 'Invalid security token. Please try again.';
    } else {
        $project_name = trim($_POST['project_name'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $client_id = (int)($_POST['client_id'] ?? 0);
        $assigned_to = (int)($_POST['assigned_to'] ?? 0);
        $start_date = $_POST['start_date'] ?? '';
        $due_date = $_POST['due_date'] ?? '';
        $budget = (float)($_POST['budget'] ?? 0);
        $status = $_POST['status'] ?? 'planning';
        $priority = $_POST['priority'] ?? 'medium';
        $progress = (int)($_POST['progress'] ?? 0);
        
        // Validation
        if (empty($project_name)) {
            $error = 'Project name is required.';
        } elseif (empty($start_date)) {
            $error = 'Start date is required.';
        } elseif (!empty($due_date) && strtotime($due_date) < strtotime($start_date)) {
            $error = 'Due date cannot be earlier than start date.';
        } elseif ($progress < 0 || $progress > 100) {
            $error = 'Progress must be between 0 and 100.';
        } else {
            // Update project
            $stmt = $mysqli->prepare("
                UPDATE projects SET 
                    project_name = ?, description = ?, client_id = ?, assigned_to = ?, 
                    start_date = ?, due_date = ?, budget = ?, status = ?, priority = ?, 
                    progress = ?, updated_at = NOW()
                WHERE id = ?
            ");
            
            $due_date_value = !empty($due_date) ? $due_date : null;
            $assigned_to_value = $assigned_to > 0 ? $assigned_to : null;
            $client_id_value = $client_id > 0 ? $client_id : null;
            
            $stmt->bind_param("ssiiisdssii", 
                $project_name, $description, $client_id_value, $assigned_to_value, 
                $start_date, $due_date_value, $budget, $status, $priority, $progress, $project_id
            );
            
            if ($stmt->execute()) {
                log_activity('Project Updated', 'projects', $project_id);
                $_SESSION['success'] = 'Project updated successfully!';
                header('Location: view.php?id=' . $project_id);
                exit();
            } else {
                $error = 'Error updating project. Please try again.';
            }
        }
    }
} else {
    // Pre-populate form with existing data
    $_POST = $project;
}

// Get clients for dropdown
$clients_query = "SELECT id, company_name FROM clients WHERE status = 'active' ORDER BY company_name";
$clients_result = $mysqli->query($clients_query);

// Get users for assignment dropdown
$users_query = "SELECT id, first_name, last_name FROM users WHERE status = 'active' ORDER BY first_name, last_name";
$users_result = $mysqli->query($users_query);

$page_title = 'Edit Project - ' . $project['project_name'];
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-pencil-square"></i>
                    Edit Project
                </h1>
                <div class="d-flex gap-2">
                    <a href="view.php?id=<?= $project['id'] ?>" class="btn btn-outline-primary">
                        <i class="bi bi-eye"></i>
                        View Project
                    </a>
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Back to Projects
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <?= htmlspecialchars($error) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Project Information</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        <?= csrf_token_input() ?>
                        
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="project_name" class="form-label">Project Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="project_name" name="project_name" 
                                       value="<?= htmlspecialchars($_POST['project_name'] ?? '') ?>" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="priority" class="form-label">Priority</label>
                                <select class="form-select" id="priority" name="priority">
                                    <option value="low" <?= ($_POST['priority'] ?? '') === 'low' ? 'selected' : '' ?>>Low</option>
                                    <option value="medium" <?= ($_POST['priority'] ?? 'medium') === 'medium' ? 'selected' : '' ?>>Medium</option>
                                    <option value="high" <?= ($_POST['priority'] ?? '') === 'high' ? 'selected' : '' ?>>High</option>
                                    <option value="urgent" <?= ($_POST['priority'] ?? '') === 'urgent' ? 'selected' : '' ?>>Urgent</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="4" 
                                      placeholder="Describe the project goals, requirements, and deliverables..."><?= htmlspecialchars($_POST['description'] ?? '') ?></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="client_id" class="form-label">Client</label>
                                <select class="form-select" id="client_id" name="client_id">
                                    <option value="">Select Client (Optional)</option>
                                    <?php while ($client = $clients_result->fetch_assoc()): ?>
                                    <option value="<?= $client['id'] ?>" 
                                            <?= ($_POST['client_id'] ?? 0) == $client['id'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($client['company_name']) ?>
                                    </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="assigned_to" class="form-label">Assign To</label>
                                <select class="form-select" id="assigned_to" name="assigned_to">
                                    <option value="">Select Team Member (Optional)</option>
                                    <?php while ($user = $users_result->fetch_assoc()): ?>
                                    <option value="<?= $user['id'] ?>" <?= ($_POST['assigned_to'] ?? 0) == $user['id'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) ?>
                                    </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="start_date" class="form-label">Start Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="start_date" name="start_date" 
                                       value="<?= $_POST['start_date'] ?? '' ?>" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="due_date" class="form-label">Due Date</label>
                                <input type="date" class="form-control" id="due_date" name="due_date" 
                                       value="<?= $_POST['due_date'] ?? '' ?>">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="budget" class="form-label">Budget</label>
                                <div class="input-group">
                                    <span class="input-group-text">₹</span>
                                    <input type="number" class="form-control" id="budget" name="budget" 
                                           value="<?= $_POST['budget'] ?? '' ?>" step="0.01" min="0" placeholder="0.00">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="planning" <?= ($_POST['status'] ?? 'planning') === 'planning' ? 'selected' : '' ?>>Planning</option>
                                    <option value="in_progress" <?= ($_POST['status'] ?? '') === 'in_progress' ? 'selected' : '' ?>>In Progress</option>
                                    <option value="on_hold" <?= ($_POST['status'] ?? '') === 'on_hold' ? 'selected' : '' ?>>On Hold</option>
                                    <option value="completed" <?= ($_POST['status'] ?? '') === 'completed' ? 'selected' : '' ?>>Completed</option>
                                    <option value="cancelled" <?= ($_POST['status'] ?? '') === 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="progress" class="form-label">Progress (%)</label>
                                <input type="number" class="form-control" id="progress" name="progress" 
                                       value="<?= $_POST['progress'] ?? 0 ?>" min="0" max="100">
                            </div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check"></i>
                                Update Project
                            </button>
                            <a href="view.php?id=<?= $project['id'] ?>" class="btn btn-outline-secondary">
                                <i class="bi bi-x"></i>
                                Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Project Details</h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless table-sm">
                        <tr>
                            <td class="fw-bold">Project ID:</td>
                            <td>#<?= $project['id'] ?></td>
                        </tr>
                        <tr>
                            <td class="fw-bold">Created:</td>
                            <td><?= format_datetime($project['created_at']) ?></td>
                        </tr>
                        <tr>
                            <td class="fw-bold">Last Updated:</td>
                            <td><?= format_datetime($project['updated_at']) ?></td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">Tips</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled small text-muted">
                        <li><i class="bi bi-info-circle text-primary"></i> Update progress to reflect current completion status.</li>
                        <li><i class="bi bi-info-circle text-primary"></i> Change status to match project phase.</li>
                        <li><i class="bi bi-info-circle text-primary"></i> Adjust due dates if timeline changes.</li>
                        <li><i class="bi bi-info-circle text-primary"></i> Reassign if team member changes.</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
