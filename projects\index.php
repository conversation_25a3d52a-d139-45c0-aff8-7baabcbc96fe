<?php
/**
 * Apex Company Management System
 * Projects List
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';
require_once '../includes/middleware.php';

// Check authentication and permission
auth_middleware();
permission_middleware('view_projects');

// Handle search and filters
$search = sanitize_input($_GET['search'] ?? '');
$status_filter = $_GET['status'] ?? '';
$client_filter = (int)($_GET['client_id'] ?? 0);

// Build query conditions
$where_conditions = [];
$params = [];
$param_types = '';

if (!empty($search)) {
    $where_conditions[] = "(p.project_name LIKE ? OR p.description LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $param_types .= 'ss';
}

if (!empty($status_filter)) {
    $where_conditions[] = "p.status = ?";
    $params[] = $status_filter;
    $param_types .= 's';
}

if ($client_filter > 0) {
    $where_conditions[] = "p.client_id = ?";
    $params[] = $client_filter;
    $param_types .= 'i';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get projects with client and assigned user info
$query = "
    SELECT p.*,
           c.company_name as client_name,
           u.first_name, u.last_name,
           (SELECT COUNT(*) FROM project_tasks pt WHERE pt.project_id = p.id) as task_count,
           (SELECT COUNT(*) FROM project_tasks pt WHERE pt.project_id = p.id AND pt.status = 'completed') as completed_tasks
    FROM projects p
    LEFT JOIN clients c ON p.client_id = c.id
    LEFT JOIN users u ON p.assigned_to = u.id
    $where_clause
    ORDER BY p.created_at DESC
";

$stmt = $mysqli->prepare($query);
if (!empty($params)) {
    $stmt->bind_param($param_types, ...$params);
}
$stmt->execute();
$projects = $stmt->get_result();

// Get clients for filter dropdown
$clients_query = "SELECT id, company_name FROM clients WHERE status = 'active' ORDER BY company_name";
$clients_result = $mysqli->query($clients_query);

$page_title = 'Projects';
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-kanban"></i>
                    Projects
                </h1>
                <?php if (has_permission('create_projects')): ?>
                <a href="add.php" class="btn btn-primary">
                    <i class="bi bi-plus"></i>
                    Add New Project
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search"
                                   value="<?= htmlspecialchars($search) ?>" placeholder="Search projects...">
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">All Statuses</option>
                                <option value="planning" <?= $status_filter === 'planning' ? 'selected' : '' ?>>Planning</option>
                                <option value="in_progress" <?= $status_filter === 'in_progress' ? 'selected' : '' ?>>In Progress</option>
                                <option value="on_hold" <?= $status_filter === 'on_hold' ? 'selected' : '' ?>>On Hold</option>
                                <option value="completed" <?= $status_filter === 'completed' ? 'selected' : '' ?>>Completed</option>
                                <option value="cancelled" <?= $status_filter === 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="client_id" class="form-label">Client</label>
                            <select class="form-select" id="client_id" name="client_id">
                                <option value="">All Clients</option>
                                <?php while ($client = $clients_result->fetch_assoc()): ?>
                                <option value="<?= $client['id'] ?>" <?= $client_filter == $client['id'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($client['company_name']) ?>
                                </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-search"></i>
                                </button>
                                <a href="index.php" class="btn btn-outline-secondary">
                                    <i class="bi bi-x"></i>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <?php if ($projects->num_rows > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Project Name</th>
                                    <th>Client</th>
                                    <th>Assigned To</th>
                                    <th>Status</th>
                                    <th>Progress</th>
                                    <th>Tasks</th>
                                    <th>Start Date</th>
                                    <th>Due Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($project = $projects->fetch_assoc()): ?>
                                <tr>
                                    <td>
                                        <strong><?= htmlspecialchars($project['project_name']) ?></strong>
                                        <?php if ($project['description']): ?>
                                            <br><small class="text-muted"><?= htmlspecialchars(substr($project['description'], 0, 100)) ?>...</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($project['client_name']): ?>
                                            <a href="../clients/view.php?id=<?= $project['client_id'] ?>" class="text-decoration-none">
                                                <?= htmlspecialchars($project['client_name']) ?>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">No Client</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?= $project['first_name'] ? htmlspecialchars($project['first_name'] . ' ' . $project['last_name']) : '<span class="text-muted">Unassigned</span>' ?>
                                    </td>
                                    <td>
                                        <span class="badge status-<?= $project['status'] ?>">
                                            <?= ucfirst(str_replace('_', ' ', $project['status'])) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar" role="progressbar" style="width: <?= $project['progress'] ?>%">
                                                <?= $project['progress'] ?>%
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?= $project['completed_tasks'] ?>/<?= $project['task_count'] ?>
                                        </span>
                                    </td>
                                    <td><?= format_date($project['start_date']) ?></td>
                                    <td>
                                        <?php if ($project['due_date']): ?>
                                            <span class="<?= strtotime($project['due_date']) < time() && $project['status'] !== 'completed' ? 'text-danger' : '' ?>">
                                                <?= format_date($project['due_date']) ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">No due date</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="view.php?id=<?= $project['id'] ?>" class="btn btn-outline-primary" title="View">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <?php if (has_permission('edit_projects')): ?>
                                            <a href="edit.php?id=<?= $project['id'] ?>" class="btn btn-outline-warning" title="Edit">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <?php endif; ?>
                                            <?php if (has_permission('delete_projects')): ?>
                                            <button type="button" class="btn btn-outline-danger" title="Delete"
                                                    onclick="deleteProject(<?= $project['id'] ?>, '<?= htmlspecialchars($project['project_name']) ?>')">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="text-center text-muted py-5">
                        <i class="bi bi-kanban" style="font-size: 4rem;"></i>
                        <h4 class="mt-3">No Projects Found</h4>
                        <p>No projects match your current filters.</p>
                        <?php if (has_permission('create_projects')): ?>
                        <a href="add.php" class="btn btn-primary mt-3">
                            <i class="bi bi-plus"></i>
                            Create First Project
                        </a>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if (has_permission('delete_projects')): ?>
<script>
function deleteProject(id, name) {
    if (confirm('Are you sure you want to delete the project "' + name + '"? This action cannot be undone.')) {
        fetch('delete.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'id=' + id + '&csrf_token=' + '<?= generate_csrf_token() ?>'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error deleting project');
        });
    }
}
</script>
<?php endif; ?>

<?php require_once '../includes/footer.php'; ?>
