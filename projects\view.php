<?php
/**
 * Apex Company Management System
 * View Project
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';
require_once '../includes/middleware.php';

// Check authentication and permission
auth_middleware();
permission_middleware('view_projects');

$project_id = (int)($_GET['id'] ?? 0);

if (!$project_id) {
    $_SESSION['error'] = 'Invalid project ID.';
    header('Location: index.php');
    exit();
}

// Get project details
$stmt = $mysqli->prepare("
    SELECT p.*, 
           c.company_name as client_name, c.id as client_id,
           u.first_name, u.last_name,
           creator.first_name as creator_first_name, creator.last_name as creator_last_name
    FROM projects p 
    LEFT JOIN clients c ON p.client_id = c.id
    LEFT JOIN users u ON p.assigned_to = u.id 
    LEFT JOIN users creator ON p.created_by = creator.id
    WHERE p.id = ?
");
$stmt->bind_param("i", $project_id);
$stmt->execute();
$result = $stmt->get_result();
$project = $result->fetch_assoc();

if (!$project) {
    $_SESSION['error'] = 'Project not found.';
    header('Location: index.php');
    exit();
}

// Get project tasks
$stmt = $mysqli->prepare("
    SELECT pt.*, u.first_name, u.last_name
    FROM project_tasks pt 
    LEFT JOIN users u ON pt.assigned_to = u.id 
    WHERE pt.project_id = ? 
    ORDER BY pt.priority DESC, pt.created_at DESC
");
$stmt->bind_param("i", $project_id);
$stmt->execute();
$tasks = $stmt->get_result();

// Get project milestones
$stmt = $mysqli->prepare("
    SELECT * FROM project_milestones 
    WHERE project_id = ? 
    ORDER BY due_date ASC
");
$stmt->bind_param("i", $project_id);
$stmt->execute();
$milestones = $stmt->get_result();

// Calculate project statistics
$total_tasks = 0;
$completed_tasks = 0;
$overdue_tasks = 0;

$tasks_copy = $tasks;
while ($task = $tasks_copy->fetch_assoc()) {
    $total_tasks++;
    if ($task['status'] === 'completed') {
        $completed_tasks++;
    }
    if ($task['due_date'] && strtotime($task['due_date']) < time() && $task['status'] !== 'completed') {
        $overdue_tasks++;
    }
}

$page_title = 'View Project - ' . $project['project_name'];
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-kanban"></i>
                    <?= htmlspecialchars($project['project_name']) ?>
                </h1>
                <div class="d-flex gap-2">
                    <?php if (has_permission('edit_projects')): ?>
                    <a href="edit.php?id=<?= $project['id'] ?>" class="btn btn-warning">
                        <i class="bi bi-pencil"></i>
                        Edit Project
                    </a>
                    <?php endif; ?>
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Back to Projects
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Project Information -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-info-circle"></i>
                        Project Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">Project Name:</td>
                                    <td><?= htmlspecialchars($project['project_name']) ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Client:</td>
                                    <td>
                                        <?php if ($project['client_name']): ?>
                                            <a href="../clients/view.php?id=<?= $project['client_id'] ?>">
                                                <?= htmlspecialchars($project['client_name']) ?>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">No Client</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Assigned To:</td>
                                    <td>
                                        <?= $project['first_name'] ? htmlspecialchars($project['first_name'] . ' ' . $project['last_name']) : '<span class="text-muted">Unassigned</span>' ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Status:</td>
                                    <td>
                                        <span class="badge status-<?= $project['status'] ?>">
                                            <?= ucfirst(str_replace('_', ' ', $project['status'])) ?>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Priority:</td>
                                    <td>
                                        <span class="badge bg-<?= $project['priority'] === 'urgent' ? 'danger' : ($project['priority'] === 'high' ? 'warning' : ($project['priority'] === 'medium' ? 'info' : 'secondary')) ?>">
                                            <?= ucfirst($project['priority']) ?>
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">Start Date:</td>
                                    <td><?= format_date($project['start_date']) ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Due Date:</td>
                                    <td>
                                        <?php if ($project['due_date']): ?>
                                            <span class="<?= strtotime($project['due_date']) < time() && $project['status'] !== 'completed' ? 'text-danger' : '' ?>">
                                                <?= format_date($project['due_date']) ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">No due date</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Budget:</td>
                                    <td>
                                        <?= $project['budget'] > 0 ? format_currency($project['budget']) : '<span class="text-muted">Not set</span>' ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Progress:</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar" role="progressbar" style="width: <?= $project['progress'] ?>%">
                                                <?= $project['progress'] ?>%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Created:</td>
                                    <td><?= format_datetime($project['created_at']) ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <?php if ($project['description']): ?>
                    <div class="mt-3">
                        <h6>Description:</h6>
                        <div class="bg-light p-3 rounded">
                            <?= nl2br(htmlspecialchars($project['description'])) ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Project Tasks -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-list-task"></i>
                        Tasks
                    </h5>
                    <?php if (has_permission('create_tasks')): ?>
                    <a href="add-task.php?project_id=<?= $project['id'] ?>" class="btn btn-sm btn-primary">
                        <i class="bi bi-plus"></i>
                        Add Task
                    </a>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <?php if ($tasks->num_rows > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Task Name</th>
                                    <th>Assigned To</th>
                                    <th>Status</th>
                                    <th>Priority</th>
                                    <th>Due Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                $tasks->data_seek(0); // Reset pointer
                                while ($task = $tasks->fetch_assoc()): 
                                ?>
                                <tr>
                                    <td>
                                        <strong><?= htmlspecialchars($task['task_name']) ?></strong>
                                        <?php if ($task['description']): ?>
                                            <br><small class="text-muted"><?= htmlspecialchars(substr($task['description'], 0, 100)) ?>...</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?= $task['first_name'] ? htmlspecialchars($task['first_name'] . ' ' . $task['last_name']) : '<span class="text-muted">Unassigned</span>' ?>
                                    </td>
                                    <td>
                                        <span class="badge status-<?= $task['status'] ?>">
                                            <?= ucfirst(str_replace('_', ' ', $task['status'])) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $task['priority'] === 'urgent' ? 'danger' : ($task['priority'] === 'high' ? 'warning' : ($task['priority'] === 'medium' ? 'info' : 'secondary')) ?>">
                                            <?= ucfirst($task['priority']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($task['due_date']): ?>
                                            <span class="<?= strtotime($task['due_date']) < time() && $task['status'] !== 'completed' ? 'text-danger' : '' ?>">
                                                <?= format_date($task['due_date']) ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">No due date</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <?php if (has_permission('edit_tasks')): ?>
                                            <a href="edit-task.php?id=<?= $task['id'] ?>" class="btn btn-outline-warning" title="Edit">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <?php endif; ?>
                                            <?php if (has_permission('delete_tasks')): ?>
                                            <button type="button" class="btn btn-outline-danger" title="Delete" 
                                                    onclick="deleteTask(<?= $task['id'] ?>, '<?= htmlspecialchars($task['task_name']) ?>')">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-list-task" style="font-size: 3rem;"></i>
                        <p class="mt-2">No tasks found for this project</p>
                        <?php if (has_permission('create_tasks')): ?>
                        <a href="add-task.php?project_id=<?= $project['id'] ?>" class="btn btn-primary">
                            <i class="bi bi-plus"></i>
                            Add First Task
                        </a>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Quick Stats -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">Project Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary"><?= $total_tasks ?></h4>
                                <small class="text-muted">Total Tasks</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success"><?= $completed_tasks ?></h4>
                            <small class="text-muted">Completed</small>
                        </div>
                    </div>
                    <?php if ($overdue_tasks > 0): ?>
                    <div class="row text-center mt-3">
                        <div class="col-12">
                            <h4 class="text-danger"><?= $overdue_tasks ?></h4>
                            <small class="text-muted">Overdue Tasks</small>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Project Details -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Project Details</h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless table-sm">
                        <tr>
                            <td class="fw-bold">Created By:</td>
                            <td>
                                <?= $project['creator_first_name'] ? htmlspecialchars($project['creator_first_name'] . ' ' . $project['creator_last_name']) : 'Unknown' ?>
                            </td>
                        </tr>
                        <tr>
                            <td class="fw-bold">Last Updated:</td>
                            <td><?= format_datetime($project['updated_at']) ?></td>
                        </tr>
                        <tr>
                            <td class="fw-bold">Project ID:</td>
                            <td>#<?= $project['id'] ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if (has_permission('delete_tasks')): ?>
<script>
function deleteTask(id, name) {
    if (confirm('Are you sure you want to delete the task "' + name + '"? This action cannot be undone.')) {
        fetch('delete-task.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'id=' + id + '&csrf_token=' + '<?= generate_csrf_token() ?>'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error deleting task');
        });
    }
}
</script>
<?php endif; ?>

<?php require_once '../includes/footer.php'; ?>
