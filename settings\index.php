<?php
/**
 * Apex Company Management System
 * Settings
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';
require_once '../includes/middleware.php';

// Check authentication and permission
auth_middleware();
permission_middleware('manage_settings');

$page_title = 'Settings';
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-gear"></i>
                    System Settings
                </h1>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="bi bi-building text-primary"></i>
                                        Company Settings
                                    </h5>
                                    <p class="card-text">Configure company information, logo, and contact details.</p>
                                    <a href="company.php" class="btn btn-outline-primary">
                                        <i class="bi bi-arrow-right"></i>
                                        Manage
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="bi bi-people text-success"></i>
                                        User Management
                                    </h5>
                                    <p class="card-text">Manage users, roles, and permissions.</p>
                                    <a href="../staff/index.php" class="btn btn-outline-success">
                                        <i class="bi bi-arrow-right"></i>
                                        Manage
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="bi bi-receipt text-info"></i>
                                        Invoice Settings
                                    </h5>
                                    <p class="card-text">Configure invoice templates, numbering, and GST settings.</p>
                                    <a href="invoices.php" class="btn btn-outline-info">
                                        <i class="bi bi-arrow-right"></i>
                                        Manage
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="bi bi-shield-check text-warning"></i>
                                        Security Settings
                                    </h5>
                                    <p class="card-text">Configure security policies, backup settings, and system logs.</p>
                                    <a href="security.php" class="btn btn-outline-warning">
                                        <i class="bi bi-arrow-right"></i>
                                        Manage
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
