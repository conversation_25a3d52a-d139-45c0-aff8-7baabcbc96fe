<?php
/**
 * Apex Company Management System
 * Staff Management
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';
require_once '../includes/middleware.php';

// Check authentication and permission
auth_middleware();
permission_middleware('view_users');

$page_title = 'Staff Management';
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-person-badge"></i>
                    Staff Management
                </h1>
                <?php if (has_permission('create_users')): ?>
                <a href="add.php" class="btn btn-primary">
                    <i class="bi bi-plus"></i>
                    Add Staff Member
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="text-center text-muted py-5">
                        <i class="bi bi-person-badge" style="font-size: 4rem;"></i>
                        <h4 class="mt-3">Staff Management</h4>
                        <p>This module is under development.</p>
                        <p>You can manage staff members, assign roles and permissions here.</p>
                        <?php if (has_permission('create_users')): ?>
                        <a href="add.php" class="btn btn-primary mt-3">
                            <i class="bi bi-plus"></i>
                            Add First Staff Member
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
